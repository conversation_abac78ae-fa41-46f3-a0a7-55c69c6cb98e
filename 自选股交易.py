import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from stock_utils import StockCodeParser, QMTConnector, DataDownloader
from stock_utils import show_time_series, check_cross_signals
import threading
import time
from datetime import datetime, timedelta
from xtquant import xtdata
import pandas as pd
import json
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
import socket
from xtquant.xtconstant import STOCK_BUY, FIX_PRICE, STOCK_SELL
import random
from signal_analyzer import SignalAnalyzer

class StockProfile:
    """股票交易档案类"""
    def __init__(self, code):
        self.code = code
        self.trades = []  # 所有交易记录列表
        self.active_positions = []  # 当前活跃的买入持仓列表
        self.historical_trades = []  # 已完成的买卖交易记录
        self.current_position = 0  # 当前持仓量
        self.total_profit = 0.0  # 总盈利
        self.trade_count = 0  # 总交易次数
        self.win_count = 0  # 盈利次数
        self.loss_count = 0  # 亏损次数
        self.last_buy_price = 0.0  # 最后买入价格
        self.last_buy_time = None  # 最后买入时间
        self.last_sell_price = 0.0  # 最后卖出价格
        self.last_sell_time = None  # 最后卖出时间
        self.max_profit = 0.0  # 最大盈利
        self.max_loss = 0.0  # 最大亏损
        self.total_fee = 0.0  # 总手续费
        self.buy_count = 0  # 当前累计买入次数，卖出后重置

    def get_last_buy(self):
        """获取最后一次买入价格"""
        return self.last_buy_price

    def get_min_buy_price_position(self):
        """获取最低买入价格的持仓信息"""
        if not self.active_positions:
            return None
        
        min_position = min(self.active_positions, key=lambda x: x['price'])
        return min_position

    def add_trade(self, trade_type, price, quantity, time_str, fee=0.0, order_id=None):
        """添加交易记录"""
        trade = {
            'type': trade_type,
            'price': price,
            'quantity': quantity,
            'time': time_str,
            'fee': fee,
            'order_id': order_id,
            'profit': None,
            'buy_count': None
        }

        if trade_type == 'buy':
            self.last_buy_price = price
            self.last_buy_time = time_str
            self.current_position += quantity
            self.buy_count += 1
            trade['buy_count'] = self.buy_count
            
            # 添加到活跃持仓列表
            self.active_positions.append({
                'price': price,
                'quantity': quantity,
                'time': time_str,
                'fee': fee,
                'order_id': order_id,
                'buy_count': self.buy_count
            })
        else:  # sell
            self.last_sell_price = price
            self.last_sell_time = time_str
            self.current_position -= quantity
            
            # 找到最低价格的买入持仓
            min_buy_position = self.get_min_buy_price_position()
            if min_buy_position:
                # 计算利润
                profit = (price - min_buy_position['price']) * quantity - fee
                self.total_profit += profit
                self.trade_count += 1
                
                if profit > 0:
                    self.win_count += 1
                    self.max_profit = max(self.max_profit, profit)
                else:
                    self.loss_count += 1
                    self.max_loss = min(self.max_loss, profit)
                
                # 更新trade记录的利润信息
                trade['profit'] = profit
                trade['buy_price'] = min_buy_position['price']
                trade['buy_time'] = min_buy_position['time']
                
                # 从活跃持仓中移除已卖出的部分
                self.active_positions.remove(min_buy_position)
                
                # 添加到历史交易记录
                completed_trade = {
                    'buy': min_buy_position,
                    'sell': trade,
                    'profit': profit
                }
                self.historical_trades.append(completed_trade)
            
            self.buy_count = 0  # 卖出后重置买入次数

        self.total_fee += fee
        self.trades.append(trade)

    def get_summary(self):
        """获取交易统计摘要"""
        win_rate = self.win_count / self.trade_count * 100 if self.trade_count > 0 else 0
        return {
            'total_profit': self.total_profit,
            'trade_count': self.trade_count,
            'win_count': self.win_count,
            'loss_count': self.loss_count,
            'win_rate': win_rate,
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'total_fee': self.total_fee,
            'current_position': self.current_position,
            'active_positions': len(self.active_positions),
            'last_buy_price': self.last_buy_price,
            'last_buy_time': self.last_buy_time,
            'last_sell_price': self.last_sell_price,
            'last_sell_time': self.last_sell_time,
            'buy_count': self.buy_count
        }

    def get_active_positions(self):
        """获取当前活跃持仓列表"""
        return self.active_positions

    def get_historical_trades(self):
        """获取历史交易记录"""
        return self.historical_trades

    def to_dict(self):
        """转换为字典格式"""
        return {
            'code': self.code,
            'trades': self.trades,
            'active_positions': self.active_positions,
            'historical_trades': self.historical_trades,
            'current_position': self.current_position,
            'total_profit': self.total_profit,
            'trade_count': self.trade_count,
            'win_count': self.win_count,
            'loss_count': self.loss_count,
            'last_buy_price': self.last_buy_price,
            'last_buy_time': self.last_buy_time,
            'last_sell_price': self.last_sell_price,
            'last_sell_time': self.last_sell_time,
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'total_fee': self.total_fee,
            'buy_count': self.buy_count
        }

    @classmethod
    def from_dict(cls, data):
        """从字典格式创建实例"""
        profile = cls(data['code'])
        profile.trades = data['trades']
        profile.active_positions = data.get('active_positions', [])  # 兼容旧数据
        profile.historical_trades = data.get('historical_trades', [])  # 兼容旧数据
        profile.current_position = data.get('current_position', 0)
        profile.total_profit = data.get('total_profit', 0.0)
        profile.trade_count = data.get('trade_count', 0)
        profile.win_count = data.get('win_count', 0)
        profile.loss_count = data.get('loss_count', 0)
        profile.last_buy_price = data.get('last_buy_price', 0.0)
        profile.last_buy_time = data.get('last_buy_time', None)
        profile.last_sell_price = data.get('last_sell_price', 0.0)  # 兼容缺失字段
        profile.last_sell_time = data.get('last_sell_time', None)  # 兼容缺失字段
        profile.max_profit = data.get('max_profit', 0.0)
        profile.max_loss = data.get('max_loss', 0.0)
        profile.total_fee = data.get('total_fee', 0.0)
        profile.buy_count = data.get('buy_count', 0)  # 兼容旧数据
        return profile

class TimeSeriesViewer:
    def __init__(self):
        # 设置日志文件 - 移到最前面
        self.setup_logging()
        
        # 初始化股票配置字典
        self.stock_profiles = {}
        
        # 初始化交易指令字典
        self.trade_instructions = {}
        
        # 新增：初始化等待盈利卖出字典
        self.waiting_for_profit_sell = {}

        # 新增：初始化最后检查分钟变量
        self.last_check_minute = -1

        # 新增：初始化已检查的时间点集合，防止重复检查
        self.checked_time_points = set()
        
        # 初始化其他属性
        self.root = tk.Tk()
        self.root.title("自选股30分钟交易系统")
        self.root.geometry("1200x800")
        
        # 定义文件路径 - 使用当日日期
        self.today = datetime.now().strftime('%Y%m%d')
        self.position_file = f"自选股30m系统持仓记录_{self.today}.json"
        self.trade_history_file = f"自选股30m系统交易历史_{self.today}.json"
        
        # 加载交易数据
        self.trading_data = self.load_trading_data()
        self.position_records = self.trading_data.get('positions', {})
        self.trade_records = self.trading_data.get('trades', [])
        self.cross_records = self.trading_data.get('cross_records', {})
        
        # 设置交易参数 - 默认启用交易
        self.trading_enabled = True
        self.monitoring_enabled = False
        self.strategy_params = {
            'max_wait_periods': 5,  # 最大等待周期数
            'min_profit': 100,      # 最小盈利金额
            'max_loss': -100,       # 最大亏损金额
            'price_adjust': 0.20    # 价格调整幅度
        }
        
        # 初始化QMT连接和数据下载器
        self.qmt = QMTConnector()
        if not self.qmt.ensure_connection():
            messagebox.showerror("错误", "无法连接到QMT")
            raise Exception("无法连接到QMT")
        
        self.downloader = DataDownloader()
        self.parser = StockCodeParser()

        # 初始化股票代码列表
        self.bond_codes = []
        self.stock_codes = []

        # 交易相关变量
        self.trading_enabled = True  # 是否启用自动交易 - 默认启用
        self.is_trading = False  # 是否正在交易

        # 添加连接状态变量
        self.is_connected = False
        
        # 创建界面元素
        self.create_widgets()
        
        # 加载股票代码
        self.load_stock_codes()
        
        # 初始化监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.order_monitor_thread = None  # 添加委托监控线程
        
        # 添加股票档案管理（先加载档案数据）
        self.stock_profiles = {}
        self.load_stock_profiles()

        # 更新持仓列表和交易汇总（在加载档案后进行，确保显示历史总盈亏）
        self.update_position_list()
        self.update_trade_summary()

        # 添加首次启动标记
        self.first_run = True

        # 启动连接状态检查
        self.check_connection()

        # 添加策略参数
        self.strategy_params = {
            'm1': 12,  # 快速EMA周期
            'm2': 50,  # 慢速EMA周期
            'n': 14,   # ATR周期
            'm': 1.5,  # ATR乘数
            'max_wait_periods': 3  # 等待第二个信号的最大周期数
        }

        # 添加策略状态变量
        self.first_cross = None  # 记录第一次交叉信号
        self.wait_periods = 0    # 等待第二个信号的周期数

        # 添加非交易时段的指标缓存
        self.non_trading_indicator_cache = {}  # 缓存非交易时段的指标计算结果

        # 添加成交回报缓存，避免重复显示
        self.processed_trades_cache = set()  # 缓存已处理的成交回报，格式: "委托号_成交时间"

        # 添加委托跟踪字典，记录每个委托的查询状态
        self.order_tracking = {}  # 格式: {order_id: {'code': code, 'last_query_time': datetime, 'query_count': int, 'is_completed': bool}}

        # 启动持仓价格定时刷新
        self.start_price_refresh()

        # 启动时自动同步服务器持仓信息
        if self.trading_enabled:
            self.add_record("程序启动，自动同步服务器持仓信息...")
            # 延迟执行同步，确保界面完全加载后再进行
            self.root.after(2000, self.auto_sync_on_startup)
    
    def auto_sync_on_startup(self):
        """启动时自动同步服务器持仓信息"""
        try:
            # 启动时同步，显示详细的同步完成信息
            success = self.sync_server_positions(show_completion_msg=True)
            if success:
                self.add_record("启动时自动同步服务器持仓成功")
            else:
                self.add_record("启动时自动同步服务器持仓失败，将在后续监控中继续尝试")
        except Exception as e:
            self.add_record(f"启动时自动同步服务器持仓出错: {str(e)}")

    def setup_logging(self):
        """设置日志文件"""
        try:
            # 创建log文件夹（如果不存在）
            log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'log')
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 设置日志文件路径
            current_date = datetime.now().strftime('%Y%m%d')
            self.log_file = os.path.join(log_dir, f'trading_log_30m_{current_date}.txt')
            
            # 如果日志文件已存在，先清空
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(f"=== 交易日志 {current_date} ===\n")
                f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            print(f"日志文件已创建: {self.log_file}")
        except Exception as e:
            print(f"设置日志文件失败: {str(e)}")
            self.log_file = None

    def write_log(self, text):
        """写入日志文件"""
        try:
            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_text = f"[{timestamp}] {text}"
            
            # 输出到日志文件
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_text + '\n')
                
        except Exception as e:
            print(f"写入日志失败: {str(e)}")
    
    def migrate_position_data(self):
        """迁移基于日期的持仓记录到永久性文件"""
        try:
            # 查找所有基于日期的持仓记录文件
            import glob
            pattern = "自选股30m系统持仓记录_*.json"
            old_files = glob.glob(pattern)

            if not old_files:
                return

            print(f"发现 {len(old_files)} 个旧的持仓记录文件，开始迁移...")

            # 合并所有持仓记录
            merged_positions = {}
            for file_path in old_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        if content:
                            positions = json.loads(content)
                            # 合并持仓记录，新的覆盖旧的
                            merged_positions.update(positions)
                            print(f"已迁移文件: {file_path}")
                except Exception as e:
                    print(f"迁移文件 {file_path} 失败: {str(e)}")

            if merged_positions:
                # 保存到新的永久性文件
                with open(self.position_file, 'w', encoding='utf-8') as f:
                    json.dump(merged_positions, f, ensure_ascii=False, indent=2)
                print(f"迁移完成，合并了 {len(merged_positions)} 条持仓记录到 {self.position_file}")

                # 备份旧文件（可选）
                backup_dir = "backup_position_files"
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                for file_path in old_files:
                    backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                    import shutil
                    shutil.move(file_path, backup_path)
                    print(f"已备份旧文件到: {backup_path}")

        except Exception as e:
            print(f"迁移持仓数据时出错: {str(e)}")

    def load_trading_data(self):
        """从文件加载交易数据"""
        # 使用固定的文件名，不依赖日期
        self.position_file = "自选股30m系统持仓记录.json"

        # 永久交易历史文件（追加模式）- 使用JSONL格式支持真正的追加
        self.permanent_trade_history_file = "自选股30m系统永久交易历史.jsonl"

        # 初始化交易记录
        self.trade_records = []
        self.cross_records = {}

        # 检查是否需要迁移旧数据
        if not os.path.exists(self.position_file):
            self.migrate_position_data()

        # 加载持仓记录（永久性文件）
        if os.path.exists(self.position_file):
            try:
                with open(self.position_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:  # 如果文件为空
                        self.position_records = {}
                        print("持仓记录文件为空，初始化空记录")
                    else:
                        self.position_records = json.loads(content)
                        print(f"已加载 {len(self.position_records)} 条持仓记录")
            except json.JSONDecodeError as e:
                print(f"持仓记录文件格式错误: {str(e)}")
                self.position_records = {}
            except Exception as e:
                print(f"加载持仓记录失败: {str(e)}")
                self.position_records = {}
        else:
            print(f"未找到持仓记录文件，初始化空记录")
            self.position_records = {}
            # 保存空记录
            self.save_trading_data()

        # 加载永久交易历史（所有历史交易记录）- 使用JSONL格式
        if os.path.exists(self.permanent_trade_history_file):
            try:
                self.trade_records = []
                with open(self.permanent_trade_history_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            try:
                                trade_record = json.loads(line)
                                self.trade_records.append(trade_record)
                            except json.JSONDecodeError:
                                print(f"跳过无效的交易记录行: {line}")
                                continue
                print(f"已加载 {len(self.trade_records)} 条历史交易记录")
            except Exception as e:
                print(f"加载永久交易历史失败: {str(e)}")
                self.trade_records = []
        else:
            print("未找到永久交易历史文件，初始化空记录")
            self.trade_records = []

        return {
            'positions': self.position_records,
            'trades': self.trade_records,
            'cross_records': self.cross_records
        }
    
    def save_trading_data(self):
        """保存交易数据到文件 - 使用追加模式保持数据连续性"""
        # 保存持仓记录 - 使用覆盖模式，因为这是当前状态快照
        with open(self.position_file, 'w', encoding='utf-8') as f:
            json.dump(self.position_records, f, ensure_ascii=False, indent=2)

        # 注意：交易历史现在通过 append_trade_to_permanent_history 实时追加
        # 不再需要批量保存当日交易历史

    def append_trade_to_permanent_history(self, trade_record):
        """将单个交易记录追加到永久交易历史文件和内存 - 使用JSONL格式真正追加"""
        try:
            # 添加到内存中的交易记录列表
            self.trade_records.append(trade_record)

            # 使用JSONL格式追加到文件（每行一个JSON对象）
            with open(self.permanent_trade_history_file, 'a', encoding='utf-8') as f:
                json_line = json.dumps(trade_record, ensure_ascii=False)
                f.write(json_line + '\n')

        except Exception as e:
            print(f"追加交易记录到永久历史失败: {str(e)}")

    def show_permanent_trade_history(self):
        """显示永久交易历史统计"""
        try:
            if not os.path.exists(self.permanent_trade_history_file):
                messagebox.showinfo("信息", "永久交易历史文件不存在")
                return

            # 读取永久交易历史（JSONL格式）
            permanent_trades = []
            with open(self.permanent_trade_history_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        try:
                            trade_record = json.loads(line)
                            permanent_trades.append(trade_record)
                        except json.JSONDecodeError:
                            continue

            if not permanent_trades:
                messagebox.showinfo("信息", "永久交易历史为空")
                return

            # 计算统计信息
            total_trades = len(permanent_trades)
            profit_trades = sum(1 for trade in permanent_trades if trade['profit'] > 0)
            loss_trades = sum(1 for trade in permanent_trades if trade['profit'] <= 0)
            total_profit = sum(trade['profit'] for trade in permanent_trades)
            total_fee = sum(trade.get('total_fee', 0) for trade in permanent_trades)

            # 按股票代码分组统计
            stock_stats = {}
            for trade in permanent_trades:
                code = trade['code']
                if code not in stock_stats:
                    stock_stats[code] = {'trades': 0, 'profit': 0, 'wins': 0, 'losses': 0}
                stock_stats[code]['trades'] += 1
                stock_stats[code]['profit'] += trade['profit']
                if trade['profit'] > 0:
                    stock_stats[code]['wins'] += 1
                else:
                    stock_stats[code]['losses'] += 1

            # 创建显示窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("永久交易历史统计")
            history_window.geometry("800x600")

            # 总体统计
            stats_frame = ttk.Frame(history_window)
            stats_frame.pack(fill=tk.X, padx=10, pady=5)

            ttk.Label(stats_frame, text=f"总交易次数: {total_trades}").grid(row=0, column=0, sticky=tk.W)
            ttk.Label(stats_frame, text=f"盈利交易: {profit_trades}").grid(row=0, column=1, sticky=tk.W)
            ttk.Label(stats_frame, text=f"亏损交易: {loss_trades}").grid(row=0, column=2, sticky=tk.W)
            ttk.Label(stats_frame, text=f"总盈亏: {total_profit:.2f}元").grid(row=1, column=0, sticky=tk.W)
            ttk.Label(stats_frame, text=f"总手续费: {total_fee:.2f}元").grid(row=1, column=1, sticky=tk.W)
            win_rate = (profit_trades / total_trades * 100) if total_trades > 0 else 0
            ttk.Label(stats_frame, text=f"胜率: {win_rate:.1f}%").grid(row=1, column=2, sticky=tk.W)

            # 分股票统计表格
            ttk.Label(history_window, text="分股票统计:").pack(anchor=tk.W, padx=10, pady=(10, 5))

            # 创建表格
            columns = ('股票代码', '交易次数', '盈利次数', '亏损次数', '总盈亏', '胜率')
            tree = ttk.Treeview(history_window, columns=columns, show='headings', height=15)

            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            # 填充数据
            for code, stats in sorted(stock_stats.items()):
                win_rate = (stats['wins'] / stats['trades'] * 100) if stats['trades'] > 0 else 0
                tree.insert('', tk.END, values=(
                    code,
                    stats['trades'],
                    stats['wins'],
                    stats['losses'],
                    f"{stats['profit']:.2f}",
                    f"{win_rate:.1f}%"
                ))

            tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            # 滚动条
            scrollbar = ttk.Scrollbar(history_window, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            messagebox.showerror("错误", f"显示永久交易历史失败: {str(e)}")

    def show_all_trades(self):
        """显示所有交易记录（包括单独的买入和卖出）"""
        try:
            if not self.trade_records:
                messagebox.showinfo("信息", "没有交易记录")
                return

            # 创建显示窗口
            trades_window = tk.Toplevel(self.root)
            trades_window.title("所有交易记录")
            trades_window.geometry("1200x700")

            # 创建笔记本控件用于分页显示
            notebook = ttk.Notebook(trades_window)
            notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 分类交易记录
            buy_trades = [record for record in self.trade_records if record.get('type') == 'buy']
            sell_trades = [record for record in self.trade_records if record.get('type') == 'sell']
            complete_trades = [record for record in self.trade_records
                             if record.get('type') == 'complete_trade' or 'profit' in record]

            # 买入记录页面
            buy_frame = ttk.Frame(notebook)
            notebook.add(buy_frame, text=f"买入记录 ({len(buy_trades)})")

            buy_columns = ('时间', '代码', '价格', '数量', '金额', '手续费', '委托号', '状态')
            buy_tree = ttk.Treeview(buy_frame, columns=buy_columns, show='headings', height=20)

            for col in buy_columns:
                buy_tree.heading(col, text=col)
                buy_tree.column(col, width=100, anchor='center')

            # 添加买入记录
            for record in sorted(buy_trades, key=lambda x: x.get('time', ''), reverse=True):
                values = (
                    record.get('time', ''),
                    record.get('code', ''),
                    f"{record.get('price', 0):.3f}",
                    record.get('quantity', 0),
                    f"{record.get('amount', 0):.2f}",
                    f"{record.get('fee', 0):.2f}",
                    record.get('order_id', ''),
                    '虚拟' if record.get('virtual', False) else '实盘'
                )
                buy_tree.insert('', 'end', values=values)

            buy_tree.pack(fill=tk.BOTH, expand=True)

            # 卖出记录页面
            sell_frame = ttk.Frame(notebook)
            notebook.add(sell_frame, text=f"卖出记录 ({len(sell_trades)})")

            sell_columns = ('时间', '代码', '价格', '数量', '金额', '手续费', '委托号', '状态', '原因')
            sell_tree = ttk.Treeview(sell_frame, columns=sell_columns, show='headings', height=20)

            for col in sell_columns:
                sell_tree.heading(col, text=col)
                sell_tree.column(col, width=100, anchor='center')

            # 添加卖出记录
            for record in sorted(sell_trades, key=lambda x: x.get('time', ''), reverse=True):
                values = (
                    record.get('time', ''),
                    record.get('code', ''),
                    f"{record.get('price', 0):.3f}",
                    record.get('quantity', 0),
                    f"{record.get('amount', 0):.2f}",
                    f"{record.get('fee', 0):.2f}",
                    record.get('order_id', ''),
                    '虚拟' if record.get('virtual', False) else '实盘',
                    record.get('reason', '')
                )
                sell_tree.insert('', 'end', values=values)

            sell_tree.pack(fill=tk.BOTH, expand=True)

            # 完整交易页面
            complete_frame = ttk.Frame(notebook)
            notebook.add(complete_frame, text=f"完整交易 ({len(complete_trades)})")

            complete_columns = ('买入时间', '卖出时间', '代码', '买入价', '卖出价', '数量', '盈亏', '盈亏%', '状态')
            complete_tree = ttk.Treeview(complete_frame, columns=complete_columns, show='headings', height=20)

            for col in complete_columns:
                complete_tree.heading(col, text=col)
                complete_tree.column(col, width=100, anchor='center')

            # 添加完整交易记录
            for record in sorted(complete_trades, key=lambda x: x.get('sell_time', ''), reverse=True):
                profit = record.get('profit', 0)
                profit_color = 'red' if profit < 0 else 'green'

                values = (
                    record.get('buy_time', ''),
                    record.get('sell_time', ''),
                    record.get('code', ''),
                    f"{record.get('buy_price', 0):.3f}",
                    f"{record.get('sell_price', 0):.3f}",
                    record.get('quantity', 0),
                    f"{profit:.2f}",
                    f"{record.get('profit_percent', 0):.2f}%",
                    '虚拟' if record.get('virtual', False) else '实盘'
                )
                item = complete_tree.insert('', 'end', values=values)
                # 根据盈亏设置颜色
                if profit < 0:
                    complete_tree.set(item, '盈亏', f"{profit:.2f}")

            complete_tree.pack(fill=tk.BOTH, expand=True)

            # 统计信息
            stats_frame = ttk.Frame(trades_window)
            stats_frame.pack(fill=tk.X, padx=10, pady=5)

            stats_text = f"买入次数: {len(buy_trades)} | 卖出次数: {len(sell_trades)} | 完整交易: {len(complete_trades)}"
            if complete_trades:
                total_profit = sum(record.get('profit', 0) for record in complete_trades)
                profit_trades = sum(1 for record in complete_trades if record.get('profit', 0) > 0)
                stats_text += f" | 总盈亏: {total_profit:.2f}元 | 盈利次数: {profit_trades}"

            ttk.Label(stats_frame, text=stats_text).pack()

        except Exception as e:
            messagebox.showerror("错误", f"显示所有交易记录失败: {str(e)}")

    def create_widgets(self):
        """创建界面元素"""
        # 创建标签框架
        control_frame = ttk.LabelFrame(self.root, text="控制面板")
        control_frame.pack(fill="x", padx=5, pady=5)
        
        # 创建按钮
        self.monitor_button = ttk.Button(control_frame, text="开始监控", command=self.toggle_monitoring)
        self.monitor_button.grid(row=0, column=0, padx=5, pady=5)
        
        self.load_button = ttk.Button(control_frame, text="加载代码", command=self.load_stock_codes)
        self.load_button.grid(row=0, column=1, padx=5, pady=5)
        
        # 导入导出按钮
        self.import_button = ttk.Button(control_frame, text="导入数据", command=self.import_trading_data)
        self.import_button.grid(row=0, column=2, padx=5, pady=5)
        
        self.export_button = ttk.Button(control_frame, text="导出数据", command=self.export_trading_data)
        self.export_button.grid(row=0, column=3, padx=5, pady=5)

        # 交易记录按钮
        self.permanent_history_button = ttk.Button(control_frame, text="交易记录", command=self.show_permanent_trade_history)
        self.permanent_history_button.grid(row=0, column=4, padx=5, pady=5)

        # 所有交易记录按钮
        self.all_trades_button = ttk.Button(control_frame, text="所有交易记录", command=self.show_all_trades)
        self.all_trades_button.grid(row=0, column=5, padx=5, pady=5)

        # 添加持仓同步按钮
        self.sync_positions_button = ttk.Button(control_frame, text="同步持仓", command=self.manual_sync_positions)
        self.sync_positions_button.grid(row=0, column=6, padx=5, pady=5)



        # 添加测试按钮
        self.test_button = ttk.Button(control_frame, text="测试功能", command=self.test_order_functions)
        self.test_button.grid(row=0, column=6, padx=5, pady=5)
        
        # 创建交易开关
        self.trading_var = tk.BooleanVar(value=True)
        self.trading_check = ttk.Checkbutton(
            control_frame, 
            text="启用交易", 
            variable=self.trading_var,
            command=self.toggle_trading
        )
        self.trading_check.grid(row=1, column=0, columnspan=2, padx=5, pady=5, sticky="w")
        
        # 添加连接状态标签
        self.connection_status_label = ttk.Label(control_frame, text="连接状态: 未连接", foreground="red")
        self.connection_status_label.grid(row=0, column=7, padx=5, pady=5)
        
        # 添加实时提示标签 - 已取消显示
        # self.realtime_hint_label = ttk.Label(control_frame, text="实时提示: 等待监控开始...", wraplength=400)
        # self.realtime_hint_label.grid(row=2, column=0, columnspan=6, padx=5, pady=5, sticky="w")

        # 添加检查状态标签
        self.check_status_label = ttk.Label(control_frame, text="检查状态: 未开始", wraplength=400)
        self.check_status_label.grid(row=3, column=0, columnspan=6, padx=5, pady=5, sticky="w")
        
        # 新增：添加持仓统计标签
        self.position_stats_label = ttk.Label(control_frame, text="当前持仓: 0 | 今日最大持仓: 0")
        self.position_stats_label.grid(row=1, column=2, columnspan=4, padx=5, pady=5, sticky="w")
        
        # 创建左右分栏
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 右侧栏 - 交易记录（先创建右侧栏，确保record_listbox最先可用）
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill="both", expand=True)
        
        # 创建记录列表
        record_frame = ttk.LabelFrame(right_frame, text="交易记录")
        record_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 添加滚动
        record_scrollbar = tk.Scrollbar(record_frame)
        record_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.record_listbox = tk.Listbox(record_frame, yscrollcommand=record_scrollbar.set)
        self.record_listbox.pack(fill="both", expand=True)
        record_scrollbar.config(command=self.record_listbox.yview)
        
        # 左侧栏 - 股票代码和持仓
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 创建可转债和ETF代码的框架
        codes_frame = ttk.Frame(left_frame)
        codes_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建可转债代码列表框
        bond_frame = ttk.LabelFrame(codes_frame, text="股票代码")
        bond_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 添加滚动条
        bond_scrollbar = tk.Scrollbar(bond_frame)
        bond_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.bond_listbox = tk.Listbox(bond_frame, yscrollcommand=bond_scrollbar.set)
        self.bond_listbox.pack(fill="both", expand=True)
        bond_scrollbar.config(command=self.bond_listbox.yview)
        
        # 创建ETF代码列表框
        #etf_frame = ttk.LabelFrame(codes_frame, text="ETF代码")
        #etf_frame.pack(side=tk.LEFT, fill="both", expand=True)
        
        # 添加滚动条
        #etf_scrollbar = tk.Scrollbar(etf_frame)
        #etf_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        #self.etf_listbox = tk.Listbox(etf_frame, yscrollcommand=etf_scrollbar.set)
        #self.etf_listbox.pack(fill="both", expand=True)
        #etf_scrollbar.config(command=self.etf_listbox.yview)
        
        # 绑定双击事件
        self.bond_listbox.bind('<Double-1>', self.show_selected_stock)
        #self.etf_listbox.bind('<Double-1>', self.show_selected_stock)
        
        # 创建持仓列表
        position_frame = ttk.LabelFrame(left_frame, text="当前持仓")
        position_frame.pack(fill="both", expand=False, padx=5, pady=5, ipady=30)

        # 创建TreeView表格显示持仓
        position_columns = ('代码', '数量', '买入价', '现价', '盈亏金额', '盈亏比例', '市值')
        self.position_tree = ttk.Treeview(position_frame, columns=position_columns, show='headings', height=8)

        # 设置列标题和宽度
        column_widths = {'代码': 80, '数量': 60, '买入价': 70, '现价': 70, '盈亏金额': 80, '盈亏比例': 80, '市值': 80}
        for col in position_columns:
            self.position_tree.heading(col, text=col, command=lambda c=col: self.sort_position_column(c))
            self.position_tree.column(col, width=column_widths.get(col, 80), anchor='center')

        # 添加滚动条
        position_scrollbar = tk.Scrollbar(position_frame, orient="vertical", command=self.position_tree.yview)
        self.position_tree.configure(yscrollcommand=position_scrollbar.set)

        # 布局TreeView和滚动条
        self.position_tree.pack(side="left", fill="both", expand=True)
        position_scrollbar.pack(side="right", fill="y")

        # 绑定点击事件到持仓表格
        self.position_tree.bind('<Double-1>', self.show_position_stock)
        self.position_tree.bind('<Button-3>', self.show_position_menu)

        # 初始化排序状态
        self.position_sort_column = None
        self.position_sort_reverse = False
        
        # 创建右键菜单
        self.position_menu = tk.Menu(self.root, tearoff=0)
        self.position_menu.add_command(label="查看分时图", command=self.view_selected_position)
        self.position_menu.add_command(label="查看交易档案", command=self.view_stock_profile)
        self.position_menu.add_command(label="清除持仓", command=self.clear_selected_position)
        
        # 创建交易汇总
        summary_frame = ttk.LabelFrame(left_frame, text="交易汇总")
        summary_frame.pack(fill="x", padx=5, pady=5)
        
        self.total_trades_label = ttk.Label(summary_frame, text="总交易次数: 0")
        self.total_trades_label.pack(anchor="w", padx=5, pady=2)
        
        self.profit_trades_label = ttk.Label(summary_frame, text="盈利交易: 0")
        self.profit_trades_label.pack(anchor="w", padx=5, pady=2)
        
        self.loss_trades_label = ttk.Label(summary_frame, text="亏损交易: 0")
        self.loss_trades_label.pack(anchor="w", padx=5, pady=2)
        
        self.total_fee_label = ttk.Label(summary_frame, text="总手续费: 0.00元")
        self.total_fee_label.pack(anchor="w", padx=5, pady=2)
        

        
        self.total_profit_label = ttk.Label(summary_frame, text="总盈亏: 0.00元")
        self.total_profit_label.pack(anchor="w", padx=5, pady=2)
    
    def load_stock_codes(self):
        """加载股票代码"""
        try:
            # 首先尝试加载当前文件夹的etf.blk文件（支持大小写）
            bond_file_path = None

            # 尝试多种可能的默认文件名
            possible_files = ['etf.blk', 'ETF.blk', 'Etf.blk']

            for filename in possible_files:
                default_file_path = os.path.join(os.getcwd(), filename)
                if os.path.exists(default_file_path):
                    # 找到默认文件，直接使用
                    bond_file_path = default_file_path
                    self.add_record(f"自动加载默认文件: {default_file_path}")
                    print(f"自动加载默认文件: {default_file_path}")
                    break

            if not bond_file_path:
                # 没有找到默认文件，弹出文件选择对话框
                self.add_record("未找到默认的etf.blk文件，请手动选择股票代码文件")
                bond_file_path = filedialog.askopenfilename(
                    title="选择股票代码文件",
                    filetypes=[("通达信板块文件", "*.blk"), ("所有文件", "*.*")],
                    initialdir=os.getcwd()  # 使用当前目录
                )

            if not bond_file_path:  # 用户取消了选择
                # 确保有默认的空列表
                if not hasattr(self, 'bond_codes'):
                    self.bond_codes = []
                if not hasattr(self, 'stock_codes'):
                    self.stock_codes = []
                return

            # 读取股票代码
            self.bond_codes = self.parser.read_stock_codes(bond_file_path)

            # 合并所有代码用于监控
            self.stock_codes = self.bond_codes

            if not self.stock_codes:
                messagebox.showwarning("警告", "未找到有效的股票代码")
                return

            # 清空并重新填充可转债列表框
            self.bond_listbox.delete(0, tk.END)
            for code in self.bond_codes:
                self.bond_listbox.insert(tk.END, code)

            # 更新状态
            total_codes = len(self.bond_codes)
            self.root.title(f"自选股30分钟交易系统 - {total_codes}只股票")

            # 记录加载成功信息
            self.add_record(f"成功加载 {len(self.bond_codes)} 只股票代码")
            print(f"成功加载 {total_codes} 只股票代码")

        except Exception as e:
            # 确保有默认的空列表
            if not hasattr(self, 'bond_codes'):
                self.bond_codes = []
            if not hasattr(self, 'stock_codes'):
                self.stock_codes = []
            messagebox.showerror("错误", f"加载股票代码失败: {str(e)}")
            print(f"加载股票代码失败: {str(e)}")
    
    def filter_stocks(self, *args):
        """根据搜索框内容过滤股票列表"""
        search_text = self.search_var.get().upper()
        self.stock_listbox.delete(0, tk.END)
        
        for code in self.stock_codes:
            if search_text in code:
                # 不再为11开头的可转债添加标记
                self.stock_listbox.insert(tk.END, code)
    
    def show_selected_stock(self, event):
        """显示选中股票的分时图"""
        # 确定是哪个列表框触发了事件
        widget = event.widget
        selection = widget.curselection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个股票")
            return
            
        code = widget.get(selection[0])
        # 移除可能的标记
        code = code.split(" ")[0]
        
        # 首先在后台线程中检查股票是否可交易
        def check_and_show():
            try:
                # 检查股票代码是否有效
                is_valid = self.is_valid_for_trading(code)
                if not is_valid:
                    self.root.after(0, lambda: messagebox.showinfo("提示", f"{code} 当前不可交易或数据不可用"))
                    return
                
                # 获取数据
                today = datetime.now().strftime('%Y%m%d')
                self.downloader.my_download([code], '1m', today, today)
                time.sleep(1)  # 等待数据就绪
                
                # 在主线程中显示图表
                self.root.after(0, lambda: self.display_chart(code))
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"准备显示分时图失败: {error_msg}"))
        
        threading.Thread(target=check_and_show).start()
    
    def display_chart(self, code):
        """显示股票分时图"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取分时数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='1m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            if not minute_data or code not in minute_data['close'].index or len(minute_data['close'].loc[code]) == 0:
                messagebox.showinfo("提示", f"无法获取 {code} 的分时数据")
                return
            
            # 创建新窗口
            chart_window = tk.Toplevel(self.root)
            chart_window.title(f"{code} 分时图")
            chart_window.geometry("800x600")
            
            # 创建图表
            fig = Figure(figsize=(10, 8), dpi=100)
            
            # 创建价格子图
            ax1 = fig.add_subplot(211)  # 2行1列的第1个
            
            # 获取时间和价格数据
            times = minute_data['time'].loc[code]
            prices = minute_data['close'].loc[code]
            
            # 计算均价线 - 使用累计成交额除以累计成交量
            amounts = minute_data['amount'].loc[code]
            volumes = minute_data['volume'].loc[code]
            
            # 计算累计成交额和累计成交量
            cumulative_amounts = np.cumsum(amounts)
            cumulative_volumes = np.cumsum(volumes)
            
            # 避免除以零
            valid_volumes = cumulative_volumes > 0
            avg_prices = np.zeros_like(prices)
            avg_prices[valid_volumes] = cumulative_amounts[valid_volumes] / cumulative_volumes[valid_volumes]
            
            # 根据股票类型调整均价除数
            if code.startswith(('15', '5')):  # ETF
                avg_prices = avg_prices / 100
            else:  # 可转债
                avg_prices = avg_prices / 10
            
            # 计算20分钟移动平均线 - 修改为使用min_periods参数
            ma20 = pd.Series(prices).rolling(window=20, min_periods=1).mean().values
            
            # 创建完整的交易时间轴
            full_time_axis = []
            
            # 上午交易时段 9:30-11:30
            morning_start = datetime.strptime(f"{today} 09:30:00", "%Y%m%d %H:%M:%S")
            morning_end = datetime.strptime(f"{today} 11:30:00", "%Y%m%d %H:%M:%S")
            current = morning_start
            while current <= morning_end:
                full_time_axis.append(current.strftime('%H:%M'))
                current += timedelta(minutes=1)
            
            # 下午交易时段 13:00-15:00
            afternoon_start = datetime.strptime(f"{today} 13:00:00", "%Y%m%d %H:%M:%S")
            afternoon_end = datetime.strptime(f"{today} 15:00:00", "%Y%m%d %H:%M:%S")
            current = afternoon_start
            while current <= afternoon_end:
                full_time_axis.append(current.strftime('%H:%M'))
                current += timedelta(minutes=1)
            
            # 格式化实际数据的时间标签
            actual_time_labels = []
            for t in times:
                if isinstance(t, datetime):
                    actual_time_labels.append(t.strftime('%H:%M'))
                else:
                    # 将毫秒时间戳转换为datetime对象
                    dt = datetime.fromtimestamp(t/1000) if t > 1000000000 else datetime.now()
                    actual_time_labels.append(dt.strftime('%H:%M'))
            
            # 创建映射，将实际数据点映射到完整时间轴上
            data_indices = []
            for label in actual_time_labels:
                if label in full_time_axis:
                    data_indices.append(full_time_axis.index(label))
                else:
                    # 如果找不到精确匹配，尝试找最接近的时间点
                    closest = min(full_time_axis, key=lambda x: abs(datetime.strptime(x, '%H:%M') - datetime.strptime(label, '%H:%M')))
                    data_indices.append(full_time_axis.index(closest))
            
            # 创建扩展的数据数组，用于在完整时间轴上绘图
            extended_prices = np.full(len(full_time_axis), np.nan)
            extended_avg_prices = np.full(len(full_time_axis), np.nan)
            extended_ma20 = np.full(len(full_time_axis), np.nan)
            extended_volumes = np.full(len(full_time_axis), 0)
            
            # 填充实际数据
            for i, idx in enumerate(data_indices):
                if idx < len(extended_prices):
                    # 修复：使用正确的索引方式访问numpy数组
                    if isinstance(prices, pd.Series):
                        extended_prices[idx] = prices.iloc[i]
                    else:
                        extended_prices[idx] = prices[i]
                        
                    if isinstance(avg_prices, pd.Series):
                        extended_avg_prices[idx] = avg_prices.iloc[i]
                    else:
                        extended_avg_prices[idx] = avg_prices[i]
                        
                    extended_ma20[idx] = ma20[i]  # ma20是numpy数组，保持不变
                    
                    if isinstance(volumes, pd.Series):
                        extended_volumes[idx] = volumes.iloc[i]
                    else:
                        extended_volumes[idx] = volumes[i]
            
            # 绘制价格线和均价线 - 使用完整时间轴
            ax1.plot(full_time_axis, extended_prices, 'b-', label='价格')
            ax1.plot(full_time_axis, extended_avg_prices, 'r-', label='均价(÷10)')
            ax1.plot(full_time_axis, extended_ma20, 'g-', label='MA20')  # 添加20分钟移动平均线
            
            # 如果有持仓，标记买入价格
            if code in self.position_records:
                position_info = self.position_records[code]

                # 处理新的队列结构
                if 'buy_queue' in position_info and position_info['buy_queue']:
                    # 新格式：计算平均买入价格
                    total_cost = position_info.get('total_cost', 0)
                    total_quantity = position_info.get('total_quantity', 0)
                    if total_quantity > 0:
                        buy_price = total_cost / total_quantity
                        ax1.axhline(y=buy_price, color='m', linestyle='--', label=f'平均买入价: {buy_price:.2f}')
                elif 'buy_price' in position_info:
                    # 旧格式：直接获取买入价格
                    buy_price = position_info['buy_price']
                    ax1.axhline(y=buy_price, color='m', linestyle='--', label=f'买入价: {buy_price:.2f}')
            
            # 设置标题和标签
            ax1.set_title(f"{code} 分时图")
            ax1.set_ylabel('价格')
            ax1.grid(True)
            ax1.legend()
            
            # 设置x轴标签 - 显示关键时间点
            key_times = ['09:30', '10:00', '10:30', '11:00', '11:30', '13:00', '13:30', '14:00', '14:30', '15:00']
            key_indices = [full_time_axis.index(t) for t in key_times if t in full_time_axis]
            
            ax1.set_xticks(key_indices)
            ax1.set_xticklabels([full_time_axis[i] for i in key_indices], rotation=45)
            
            # 创建成交量子图
            ax2 = fig.add_subplot(212, sharex=ax1)  # 2行1列的第2个，共享x轴
            
            # 绘制成交量柱状图 - 使用完整时间轴
            if np.sum(extended_volumes) > 0:
                bars = ax2.bar(range(len(full_time_axis)), extended_volumes, width=0.8, color='g', alpha=0.5)
                
                # 设置y轴范围
                max_volume = np.max(extended_volumes)
                if max_volume > 0:
                    ax2.set_ylim(0, max_volume * 1.2)
                    
                    # 为较大的成交量添加数值标签
                    threshold = max_volume * 0.5
                    for i, v in enumerate(extended_volumes):
                        if v >= threshold:
                            ax2.text(i, v, str(int(v)), ha='center', va='bottom', fontsize=8)
            else:
                ax2.text(0.5, 0.5, '无成交量数据', 
                        horizontalalignment='center',
                        verticalalignment='center',
                        transform=ax2.transAxes)
            
            # 设置标签
            ax2.set_ylabel('成交量')
            ax2.set_xlabel('时间')
            ax2.grid(True)
            
            # 确保x轴标签与价格图对齐
            ax2.set_xticks(ax1.get_xticks())
            ax2.set_xticklabels(ax1.get_xticklabels())
            
            # 调整布局
            fig.tight_layout()
            
            # 将图表添加到窗口
            canvas = FigureCanvasTkAgg(fig, master=chart_window)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            toolbar = NavigationToolbar2Tk(canvas, chart_window)
            toolbar.update()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
        except Exception as e:
            messagebox.showerror("错误", f"显示分时图失败: {str(e)}")
            print(f"显示分时图失败: {str(e)}")
    
    def display_5min_chart(self, code):
        """使用5分钟数据显示分时图"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 尝试获取5分钟数据
            self.downloader.my_download([code], '5m', today, today)
            time.sleep(1)  # 等待数据就绪
            
            # 获取5分钟K线数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='5m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            # 检查是否获取到数据
            if minute_data is None or code not in minute_data['time'].index or len(minute_data['time'].loc[code]) == 0:
                print(f"无法获取{code}的5分钟数据")
                messagebox.showinfo("提示", f"无法获取{code}的分时数据，请使用K线图查看")
                return
                
            # 创建DataFrame
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.tz_localize(None),
                'open': minute_data['open'].loc[code],
                'close': minute_data['close'].loc[code],
                'high': minute_data['high'].loc[code],
                'low': minute_data['low'].loc[code],
                'volume': minute_data['volume'].loc[code],
                'amount': minute_data['amount'].loc[code]
            })
            
            # 过滤交易时间范围（9:30-15:00）
            df = df[
                (df['time'].dt.time >= pd.Timestamp('09:30:00').time()) &
                (df['time'].dt.time <= pd.Timestamp('15:00:00').time())
            ]
            
            # 绘制分时图
            from stock_utils import plot_time_series
            plot_time_series(df, f"{code} 分时图 (5分钟)")
            
        except Exception as e:
            print(f"显示5分钟分时图时出错: {str(e)}")
            messagebox.showerror("错误", f"显示分时图失败: {str(e)}")
    
    def is_trading_time(self):
        """检查当前是否在交易时间"""
        current_time = datetime.now().time()
        # 交易时间：9:30-11:30, 13:00-15:00
        return (('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or
                ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '15:00:00'))

    def is_virtual_trading(self):
        """检查当前是否是虚拟交易模式"""
        return not self.trading_enabled

    def is_valid_for_trading(self, code, timeout=3):
        """检查股票是否可交易且数据可用，使用线程超时机制"""
        result = [False]
        error_msg = [None]
        
        def check_trading():
            try:
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 尝试获取分时数据，使用正确的参数格式
                self.downloader.my_download([code], '1m', today, today)
                
                # 等待数据就绪
                time.sleep(1)
                
                # 获取1分钟K线数据
                minute_data = xtdata.get_market_data(
                    field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                    stock_list=[code],
                    period='1m',
                    start_time=today,
                    end_time=today,
                    count=-1
                )
                
                # 检查返回的数据是否有效
                if minute_data and code in minute_data['time'].index and len(minute_data['time'].loc[code]) > 0:
                    result[0] = True
                else:
                    result[0] = False
            except Exception as e:
                error_msg[0] = str(e)
                result[0] = False
                # 只打印错误，不使用messagebox
                print(f"检查股票{code}交易状态时出错: {str(e)}")
        
        # 创建并启动检查线程
        check_thread = threading.Thread(target=check_trading)
        check_thread.daemon = True
        check_thread.start()
        
        # 等待线程完成，带超时
        check_thread.join(timeout)
        
        # 如果线程仍在运行，说明超时了
        if check_thread.is_alive():
            print(f"检查股票{code}交易状态超时")
            return False
        
        return result[0]
    
    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_button.config(text="停止监控")
            self.update_realtime_hint("监控已启动...")

            # 清空当天的已检查时间点，确保重新开始监控时能正常检查
            current_date_prefix = datetime.now().strftime('%Y%m%d')
            self.checked_time_points = {
                key for key in self.checked_time_points
                if key.startswith(current_date_prefix)
            }

            # 开始监控时进行一次完整的买卖条件判定
            self.add_record("开始监控，进行首次买卖点判定...")
            self.update_check_status("监控已启动，等待检查时间点...")
            self.check_trading_signals()

            # 创建并启动监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_stocks)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()

            # 创建并启动委托监控线程
            self.order_monitor_thread = threading.Thread(target=self.monitor_orders)
            self.order_monitor_thread.daemon = True
            self.order_monitor_thread.start()

            print("开始监控")
        else:
            # 停止监控
            self.monitoring = False
            self.monitor_button.config(text="开始监控")
            self.update_realtime_hint("监控已停止")
            
            # 等待线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(1.0)
            if self.order_monitor_thread and self.order_monitor_thread.is_alive():
                self.order_monitor_thread.join(1.0)
            
            print("停止监控")
    
    def toggle_trading(self):
        """切换自动交易状态"""
        self.trading_enabled = self.trading_var.get()
        status = "启用" if self.trading_enabled else "禁用"
        self.add_record(f"自动交易已{status}")

        # 当启用交易时，自动同步服务器持仓
        if self.trading_enabled:
            self.add_record("启用交易，开始同步服务器持仓...")
            # 延迟执行同步，避免阻塞界面
            self.root.after(1000, self.auto_sync_on_startup)
    
    def export_trade_records(self):
        """导出交易记录到CSV文件"""
        if not self.trade_records:
            messagebox.showinfo("提示", "没有交易记录可导出")
            return
            
        try:
            # 获取当前日期时间作为文件名
            now = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"交易记录_{now}.csv"
            
            # 创建DataFrame并导出
            df = pd.DataFrame(self.trade_records)
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            messagebox.showinfo("成功", f"交易记录已导出至 {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出交易记录失败: {str(e)}")
    
    def monitor_stocks(self):
        """监控股票"""
        try:
            while True:
                try:
                    # 获取当前时间
                    current_time = datetime.now()
                    current_hour = current_time.hour
                    current_minute = current_time.minute
                    current_second = current_time.second  # 添加秒数
                    
                    # 判断是否在交易时间
                    if not (
                        (current_hour == 9 and current_minute >= 30) or  # 9:30-11:30
                        (current_hour > 9 and current_hour < 11) or
                        (current_hour == 11 and current_minute <= 30) or
                        (current_hour >= 13 and current_hour < 15) or  # 13:00-14:59
                        (current_hour == 15 and current_minute == 0)  # 15:00收盘时刻
                    ):
                        # 非交易时间
                        if current_hour < 9:
                            pass
                        elif current_hour >= 15:
                            # self.update_realtime_hint("已收盘")  # 取消实时提示
                            # 收盘后清理数据
                            if not hasattr(self, 'data_cleaned'):
                                self.save_trading_data()
                                self.data_cleaned = True
                            else:
                                time.sleep(60)   # 收盘后每分钟检查一次
                            continue
                        
                        time.sleep(30)  # 非交易时间每30秒检查一次，降低资源占用
                        continue
                    
                    # 在交易时间，检查是否到了30分钟K线的时间点的25秒
                    # 30分钟K线时间点：9:30:25, 10:00:25, 10:30:25, 11:00:25, 13:00:25, 13:30:25, 14:00:25, 14:30:25
                    # 注意：11:30:25和15:00:25不检查，因为是收盘时间
                    trading_time_points = [
                        (9, 30), (10, 0), (10, 30), (11, 0),
                        (13, 0), (13, 30), (14, 0), (14, 30)
                    ]

                    # 检查当前时间是否为30分钟K线时间点的25秒
                    current_time_point = (current_hour, current_minute)

                    # 在每个时间点的23-27秒范围内进行检查（给5秒的容错时间）
                    if (current_time_point in trading_time_points and
                        23 <= current_second <= 27):
                        # 创建当天的时间点标识
                        today_time_key = f"{datetime.now().strftime('%Y%m%d')}_{current_hour:02d}_{current_minute:02d}"

                        # 如果这个时间点还没有检查过，则进行检查
                        if today_time_key not in self.checked_time_points:
                            print(f"执行检查 - 当前时间: {current_time.strftime('%H:%M:%S')}")  # 调试输出
                            self.add_record(f"进行30分钟买卖点判定... 时间: {current_time.strftime('%H:%M:%S')}")
                            self.update_check_status(f"正在检查 {current_hour:02d}:{current_minute:02d}:25")
                            self.check_trading_signals()

                            # 标记这个时间点已经检查过
                            self.checked_time_points.add(today_time_key)
                            self.update_check_status(f"已检查 {current_hour:02d}:{current_minute:02d}:25")

                            # 清理过期的时间点记录（保留当天的记录）
                            current_date_prefix = datetime.now().strftime('%Y%m%d')
                            self.checked_time_points = {
                                key for key in self.checked_time_points
                                if key.startswith(current_date_prefix)
                            }

                    # 显示当前等待状态和已检查的时间点
                    if current_time_point in trading_time_points:
                        if current_second < 23:
                            self.update_check_status(f"等待 {current_hour:02d}:{current_minute:02d}:23-27 ({23-current_second}秒后)")
                        elif current_second > 27:
                            checked_times = [key.split('_')[1] + ':' + key.split('_')[2] for key in self.checked_time_points if key.startswith(datetime.now().strftime('%Y%m%d'))]
                            if checked_times:
                                self.update_check_status(f"今日已检查: {', '.join(sorted(checked_times))}")
                            else:
                                self.update_check_status("等待下一个检查时间点...")
                    
                    # 备用检查机制：只在非23-27秒时刻检查是否有错过的时间点需要补检
                    if not (23 <= current_second <= 27):  # 避免与正常检查冲突
                        for check_hour, check_minute in trading_time_points:
                            check_time_key = f"{datetime.now().strftime('%Y%m%d')}_{check_hour:02d}_{check_minute:02d}"

                            # 计算与该时间点25秒的时间差
                            check_time_25s = datetime.now().replace(hour=check_hour, minute=check_minute, second=25, microsecond=0)
                            time_diff = (current_time - check_time_25s).total_seconds()

                            # 如果当前时间超过该时间点的25秒后60-180秒，且该时间点未检查过，则立即补检
                            if (60 <= time_diff <= 180 and
                                check_time_key not in self.checked_time_points and
                                check_time_25s.date() == current_time.date()):

                                print(f"备用检查 - 补检时间点: {check_hour:02d}:{check_minute:02d}:25, 当前时间: {current_time.strftime('%H:%M:%S')}")
                                self.add_record(f"备用检查 - 补检30分钟买卖点判定... 时间点: {check_hour:02d}:{check_minute:02d}:25")
                                self.update_check_status(f"备用检查 {check_hour:02d}:{check_minute:02d}:25")
                                self.check_trading_signals()

                                # 标记这个时间点已经检查过
                                self.checked_time_points.add(check_time_key)
                                self.update_check_status(f"已补检 {check_hour:02d}:{check_minute:02d}:25")
                                break  # 只补检一个时间点，避免重复

                    time.sleep(5)  # 优化检查间隔，降低资源占用
                    
                except Exception as e:
                    self.add_record(f"监控股票时出错: {str(e)}")
                    time.sleep(10)
                
        except Exception as e:
            self.add_record(f"监控股票时出错: {str(e)}")
            time.sleep(10)

    def _get_and_prepare_data(self, code, start_time, end_time):
        """获取并准备数据"""
        try:
            # 先从服务器下载5分钟数据以保证数据更新
            try:
                xtdata.download_history_data(
                    code,
                    period='5m',
                    start_time=start_time,
                    end_time=end_time,
                    incrementally=False  # 全量下载，确保数据最新
                )
            except Exception as download_error:
                print(f"[警告] {code} 下载5分钟数据失败: {str(download_error)}")

            # 再从服务器下载最新30分钟数据（参考测试程序的成功模式）
            try:
                xtdata.download_history_data(
                    code,
                    period='30m',
                    start_time=start_time,
                    end_time=end_time,
                    incrementally=False  # 全量下载，确保数据最新
                )
            except Exception as download_error:
                print(f"[警告] {code} 下载30分钟数据失败: {str(download_error)}, 尝试使用本地数据")

            # 然后获取30分钟K线数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='30m',
                start_time=start_time,
                end_time=end_time,
                count=-1
            )

            # 检查数据是否有效
            if minute_data is None or len(minute_data) == 0 or 'time' not in minute_data:
                raise Exception("获取到的数据为空")

            if code not in minute_data['time'].index:
                raise Exception(f"股票代码{code}不在返回的数据中")

            # 创建DataFrame
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.tz_localize(None),
                'open': minute_data['open'].loc[code],
                'close': minute_data['close'].loc[code],
                'high': minute_data['high'].loc[code],
                'low': minute_data['low'].loc[code],
                'volume': minute_data['volume'].loc[code],
                'amount': minute_data['amount'].loc[code]
            })

            # 过滤交易时间范围（9:30-15:00）和日期范围（不超过当前日期）
            current_datetime = datetime.now()
            current_date = current_datetime.date()
            df = df[
                (df['time'].dt.time >= pd.Timestamp('09:30:00').time()) &
                (df['time'].dt.time <= pd.Timestamp('15:00:00').time()) &
                (df['time'].dt.date <= current_date)  # 添加日期过滤，排除未来日期
            ]

            # 检查数据的时效性（参考测试程序的逻辑）
            if not df.empty and pd.notna(df['time'].max()):
                latest_data_time = df['time'].max()
                current_time_str = current_datetime.strftime('%Y-%m-%d %H:%M')
                latest_time_str = latest_data_time.strftime('%Y-%m-%d %H:%M')

                print(f"[调试] {code} 当前时间: {current_time_str}, 最新数据时间: {latest_time_str}, 过滤后数据条数: {len(df)}")
            else:
                print(f"[错误] {code} ❌ 数据为空或无效")
                return None

            # 计算技术指标
            df = self.calculate_indicators(df)
            if df is None:
                raise Exception("指标计算失败")

            return df

        except Exception as e:
            print(f"[调试] {code} 获取数据失败: {str(e)}")
            return None

    def check_trading_signals(self):
        try:
            # 获取当前时间
            now = datetime.now()
            today = now.strftime('%Y%m%d')
            current_time = now.time()
            current_hour = now.hour
            current_minute = now.minute

            # 判断是否在开盘前
            is_before_open = current_time < datetime.strptime('09:30:00', '%H:%M:%S').time()

            # 检查股票代码是否已加载
            if not hasattr(self, 'bond_codes') or not self.bond_codes:
                self.add_record("股票代码未加载，请先加载股票代码文件")
                return

            # 合并可转债和ETF代码作为监控列表
            monitor_codes = self.bond_codes
            total_codes = len(monitor_codes)
            self.add_record(f"开始检查 {total_codes} 只股票的买卖信号")
            
            # 获取数据的时间范围
            end_time = today
            start_time = (now - timedelta(days=20)).strftime('%Y%m%d')  # 获取20个交易日的数据
            
            # 记录成功获取数据的股票
            processed_codes = []
            failed_codes = []
            
            print(f"\n[调试] 开始获取30分钟K线数据，时间范围: {start_time} 至 {end_time}")
            print(f"[调试] 当前系统时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"[调试] 当前时间戳: {int(now.timestamp() * 1000)}")
            
            # 检查每个股票
            for code in monitor_codes:
                try:
                    signal_text = None  # 初始化signal_text变量

                    # 获取并准备数据
                    df = self._get_and_prepare_data(code, start_time, end_time)
                    if df is None or df.empty:
                        raise Exception("数据获取失败或为空")

                    # 获取当前K线和前一K线数据
                    current_kline_data = self.get_current_kline_data(df, current_hour, current_minute)
                    prev_kline_data = self.get_prev_kline_data(df, current_hour, current_minute)

                    # 检查K线数据是否足够
                    if current_kline_data.empty or prev_kline_data.empty:
                        print(f"[调试] {code} K线数据不足 - 当前K线: {len(current_kline_data)}条, 前一K线: {len(prev_kline_data)}条")
                        print(f"[调试] {code} 使用时间点: {current_hour:02d}:{current_minute:02d}")

                        # 打印所有可用的时间点
                        available_times = df['time'].dt.strftime('%H:%M').unique()
                        print(f"[调试] {code} 可用的K线时间点: {sorted(available_times)}")

                        raise Exception("K线数据不足")

                    # 获取最新价格和对应时间
                    current_price = current_kline_data['close'].iloc[-1]
                    current_price_time = current_kline_data['time'].iloc[-1]

                    # 检查交易条件
                    has_signal, signal_type, signal_value, signal_indicator, analysis_result = self._check_trading_conditions(df, current_price, code, current_price_time)

                    # 如果有交易信号，处理买卖信号
                    if has_signal:
                        signal_text = self._process_trading_signals(code, signal_type, current_price, is_before_open)

                    # 检查止损条件
                    if (code in self.stock_profiles and
                        self.stock_profiles[code].get_summary()['current_position'] > 0 and
                        code in self.position_records):
                        self._check_stop_conditions(code, current_price, is_before_open)

                    # 打印分析结果
                    self.print_cross_analysis(code, analysis_result)

                    # 记录处理成功的股票
                    processed_codes.append(code)

                except Exception as e:
                    print(f"[调试] 处理 {code} 时出错: {str(e)},检查是什么原因")
                    import traceback
                    traceback.print_exc()
                    failed_codes.append(code)
                    continue
            
            # 打印处理结果
            total = len(monitor_codes)
            success = len(processed_codes)
            failed = len(failed_codes)
            self.add_record(f"处理完成: 总计{total}只股票, 成功{success}只, 失败{failed}只")
            if failed > 0:
                self.add_record(f"失败列表: {', '.join(failed_codes)}")
            
        except Exception as e:
            error_msg = f"检查交易信号时出错: {str(e)}"
            print(error_msg)
            self.add_record(error_msg)

    def _process_trading_signals(self, code, cross_type, current_price, is_before_open):
        """处理交易信号"""
        signal_text = None  # 初始化为None
        if cross_type == 'golden':
            signal_text = f"{code} 出现买入信号: 价格({current_price:.3f})"
            if not is_before_open and self.is_trading_time():
                self.execute_buy(code, current_price, datetime.now().strftime('%H:%M:%S'))
            else:
                reason = "[开盘前]" if is_before_open else "[非交易时段]"
                self.add_record(f"{reason} {signal_text} - 暂不执行买入")
        elif cross_type == 'death' and code in self.position_records:
            signal_text = f"{code} 出现卖出信号: 价格({current_price:.3f})"
            if not is_before_open and self.is_trading_time():
                self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
            else:
                reason = "[开盘前]" if is_before_open else "[非交易时段]"
                self.add_record(f"{reason} {signal_text} - 暂不执行卖出")
        
        if signal_text:  # 只有当signal_text不为None时才更新提示
            self.add_record(signal_text)
            self.root.after(0, lambda t=signal_text: self.update_realtime_hint(t))

    def _check_stop_conditions(self, code, current_price, is_before_open):
        """检查止损止盈条件"""
        position_info = self.position_records[code]

        # 处理新的队列结构
        if 'buy_queue' in position_info:
            # 新格式：使用队列结构
            if not position_info['buy_queue']:
                return  # 没有买入记录

            # 计算平均买入价格
            total_cost = position_info.get('total_cost', 0)
            total_quantity = position_info.get('total_quantity', 0)
            if total_quantity == 0:
                return

            buy_price = total_cost / total_quantity
            quantity = total_quantity
        else:
            # 旧格式：直接获取买入价格
            buy_price = position_info.get('buy_price')
            quantity = position_info.get('quantity', 100)
            if buy_price is None:
                return

        current_profit = (current_price - buy_price) * quantity

        # 取消强制止损功能
        # if current_profit <= -200:
        #     if not is_before_open and self.is_trading_time():
        #         self.execute_protective_sell(code, current_price, datetime.now().strftime('%H:%M:%S'), buy_price)
        #     else:
        #         reason = "[开盘前]" if is_before_open else "[非交易时段]"
        #         self.add_record(f"{reason} {code} 触发强制止损 - 暂不执行卖出")

        # 强制止盈
        if current_profit >= 4000:
            if not is_before_open and self.is_trading_time():
                self.execute_protective_sell(code, current_price, datetime.now().strftime('%H:%M:%S'), buy_price)
            else:
                reason = "[开盘前]" if is_before_open else "[非交易时段]"
                self.add_record(f"{reason} {code} 触发强制止盈 - 暂不执行卖出")

    def send_request(self, request_data):
        """发送请求到委托查询撤单程序"""
        client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            client.connect(('localhost', 9876))
            client.sendall(json.dumps(request_data).encode('utf-8'))
            client.shutdown(socket.SHUT_WR)
            
            data = b""
            while True:
                chunk = client.recv(4096)
                if not chunk:
                    break
                data += chunk
            
            response = json.loads(data.decode('utf-8'))
            return response
            
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
            return {"status": "error", "message": str(e)}
        finally:
            client.close()

    def execute_buy(self, code, price, time_str):
        """执行买入操作"""
        try:
            # 计算可以买入的股数（100股为基数）
            target_amount = 10000  # 目标金额改为10000元
            quantity = int((target_amount / price) // 100) * 100

            # 确保至少买入100股
            if quantity < 100:
                quantity = 100
            
            # 根据股票类型调整买入价格加价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.20
            
            # 买入价格加价
            buy_price = price + price_adjust
            
            # 规范化股票代码格式
            code = code.upper()
            
            # 获取当前数据值
            today = datetime.now().strftime('%Y%m%d')
            # 先下载5分钟数据以保证数据更新
            self.downloader.my_download([code], '5m', today, today)
            time.sleep(1)
            minute_data = xtdata.get_market_data(
                field_list=['time', 'open', 'close', 'high', 'low', 'volume', 'amount'],
                stock_list=[code],
                period='5m',
                start_time=today,
                end_time=today,
                count=-1
            )
            
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.tz_localize(None),
                'open': minute_data['open'].loc[code],
                'close': minute_data['close'].loc[code],
                'high': minute_data['high'].loc[code],
                'low': minute_data['low'].loc[code],
                'volume': minute_data['volume'].loc[code],
                'amount': minute_data['amount'].loc[code]
            })
            
            # 过滤交易时间范围（9:30-15:00）
            df = df[
                (df['time'].dt.time >= pd.Timestamp('09:30:00').time()) &
                (df['time'].dt.time <= pd.Timestamp('15:00:00').time())
            ]
            
            # 计算指标
            df = self.calculate_indicators(df)
            # 移除exp3相关逻辑，当前策略只使用CCI和EMA50
            below_exp3_at_buy = False  # 设置为默认值
            
            if self.trading_enabled:
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_BUY,
                        'volume': quantity,
                        'price_type': FIX_PRICE,
                        'price': buy_price
                    }
                }
                
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"买入委托失败 {code}: {response.get('message', '未知错误')}")
                    return
                
                order_id = response['data']['order_id']
                self.add_record(f"买入委托已提交 {code} 委托号: {order_id} 价格: {buy_price:.2f}")

                # 存储买入指令
                self.trade_instructions[code] = {
                    'type': 'buy',
                    'order_id': order_id,
                    'time': time_str,
                    'price': buy_price
                }

                # 添加委托跟踪
                self.order_tracking[order_id] = {
                    'code': code,
                    'last_query_time': datetime.now(),
                    'query_count': 0,
                    'is_completed': False,
                    'order_type': 'buy'
                }

                # 立即查询一次成交状态
                self.query_trade_status(code, order_id)
            else:
                order_id = f"VIRTUAL_{int(time.time())}"
                self.add_record(f"[虚拟]买入委托 {code} 价格: {buy_price:.2f}")
            
            # 更新持仓记录 - 改为支持多次买入的队列结构
            if code not in self.position_records:
                self.position_records[code] = {
                    'buy_queue': [],  # 买入队列，按时间顺序存储每次买入
                    'total_quantity': 0,  # 总持仓数量
                    'total_cost': 0.0,  # 总成本
                    'total_fee': 0.0   # 总手续费
                }

            # 添加新的买入记录到队列
            buy_record = {
                'buy_price': float(buy_price),
                'buy_time': time_str,
                'quantity': int(quantity),
                'fee': float(buy_price * quantity * 0.0001),
                'actual_amount': float(buy_price * quantity),
                'order_id': order_id,
                'virtual': bool(not self.trading_enabled),
                'below_exp3_at_buy': below_exp3_at_buy,
                'crossed_exp3': bool(not below_exp3_at_buy)
            }

            # 将买入记录添加到队列
            self.position_records[code]['buy_queue'].append(buy_record)

            # 更新总计数据
            self.position_records[code]['total_quantity'] += quantity
            self.position_records[code]['total_cost'] += buy_price * quantity
            self.position_records[code]['total_fee'] += buy_price * quantity * 0.0001
            
            # 更新持仓列表显示
            self.update_position_list()
            
            # 保存交易数据
            self.save_trading_data()

            # 记录买入操作到交易历史
            buy_trade_record = {
                'code': code,
                'type': 'buy',  # 交易类型：买入
                'price': buy_price,
                'quantity': quantity,
                'time': time_str,
                'order_id': order_id,
                'fee': buy_price * quantity * 0.0001,
                'amount': buy_price * quantity,
                'virtual': not self.trading_enabled,
                'timestamp': datetime.now().isoformat()
            }

            # 追加到永久交易历史文件
            self.append_trade_to_permanent_history(buy_trade_record)

            # 更新股票档案
            if code not in self.stock_profiles:
                self.stock_profiles[code] = StockProfile(code)

            # 计算手续费
            fee = price * quantity * 0.0001

            # 添加买入记录
            self.stock_profiles[code].add_trade('buy', price, quantity, time_str, fee, order_id)

            # 保存档案
            self.save_stock_profiles()
            
        except Exception as e:
            self.add_record(f"买入 {code} 失败: {str(e)}")
    
    def execute_sell(self, code, price, time_str):
        """执行卖出操作 - 每次只卖出一个单位，按FIFO原则"""
        try:
            if code not in self.position_records:
                return

            position_info = self.position_records[code]
            buy_queue = position_info.get('buy_queue', [])

            if not buy_queue:
                # 检查是否是旧格式的持仓记录
                if 'buy_price' in position_info and 'quantity' in position_info:
                    # 检查数量是否为0，如果为0说明已经卖出，不应该转换
                    old_quantity = position_info['quantity']
                    if old_quantity <= 0:
                        self.add_record(f"[调试] {code} 发现旧格式持仓记录但数量为0，直接清除")
                        del self.position_records[code]
                        self.update_position_list()
                        self.save_trading_data()
                        return

                    # 再次确认服务器持仓状态，避免转换已经卖出的持仓
                    if self.trading_enabled and not self.is_virtual_trading():
                        request = {'type': 'query_positions'}
                        response = self.send_request(request)

                        if response['status'] == 'success':
                            server_has_position = False
                            for position in response['data']:
                                if position.get('stock_code') == code and position.get('volume', 0) > 0:
                                    server_has_position = True
                                    break

                            if not server_has_position:
                                self.add_record(f"[调试] {code} 服务器无持仓，清除旧格式记录")
                                del self.position_records[code]
                                self.update_position_list()
                                self.save_trading_data()
                                return

                    self.add_record(f"[调试] {code} 发现旧格式持仓记录，转换为新格式")
                    # 转换旧格式为新格式
                    old_buy_price = position_info['buy_price']
                    old_buy_time = position_info.get('buy_time', datetime.now().strftime('%H:%M:%S'))
                    old_fee = position_info.get('fee', old_buy_price * old_quantity * 0.0001)
                    old_virtual = position_info.get('virtual', True)  # 默认为虚拟交易

                    # 创建新格式的持仓记录
                    new_position_info = {
                        'buy_queue': [{
                            'buy_price': float(old_buy_price),
                            'buy_time': old_buy_time,
                            'quantity': int(old_quantity),
                            'fee': float(old_fee),
                            'actual_amount': float(old_buy_price * old_quantity),
                            'order_id': position_info.get('order_id', f"OLD_{int(time.time())}"),
                            'virtual': bool(old_virtual),
                            'below_exp3_at_buy': position_info.get('below_exp3_at_buy', False),
                            'crossed_exp3': position_info.get('crossed_exp3', False)
                        }],
                        'total_quantity': int(old_quantity),
                        'total_cost': float(old_buy_price * old_quantity),
                        'total_fee': float(old_fee)
                    }

                    # 更新持仓记录
                    self.position_records[code] = new_position_info
                    buy_queue = new_position_info['buy_queue']
                    position_info = new_position_info

                    # 保存更新后的数据
                    self.save_trading_data()
                    self.add_record(f"[调试] {code} 旧格式转换完成，继续卖出操作")
                else:
                    self.add_record(f"{code} 没有可卖出的持仓，但在持仓列表中有")
                    # 添加调试信息
                    self.add_record(f"[调试] {code} 持仓记录结构: {position_info}")
                    self.add_record(f"[调试] trading_enabled: {self.trading_enabled}")
                    self.add_record(f"[调试] is_virtual_trading: {self.is_virtual_trading()}")
                    # 清理无效的持仓记录
                    del self.position_records[code]
                    self.update_position_list()
                    self.save_trading_data()
                    return

            # 获取最早的买入记录（FIFO）
            earliest_buy = buy_queue[0]

            # 卖出数量等于该次买入的全部数量
            sell_quantity = earliest_buy['quantity']

            self.add_record(f"{code} 准备卖出第一次买入的全部数量: {sell_quantity}股")

            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40
            
            # 获取买入信息
            buy_price = earliest_buy['buy_price']
            buy_time = earliest_buy['buy_time']
            buy_fee = earliest_buy['fee'] * (sell_quantity / earliest_buy['quantity'])  # 按比例计算手续费

            # 智能判断交易模式：无论本地记录如何，都先查询服务器持仓来确定实际交易状态
            is_real_trade = False
            actual_volume = 0

            if self.trading_enabled and not self.is_virtual_trading():
                # 查询服务器实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)

                if response['status'] == 'success':
                    # 检查服务器是否有该股票的持仓
                    for position in response['data']:
                        if position['stock_code'] == code:
                            is_real_trade = True
                            actual_volume = position['volume']
                            self.add_record(f"服务器确认 {code} 实际持仓 {actual_volume} 股，按实际交易处理")
                            break

                    if not is_real_trade:
                        self.add_record(f"服务器未找到 {code} 的实际持仓，按虚拟交易处理")
                else:
                    self.add_record(f"查询服务器持仓失败: {response.get('message', '未知错误')}，按虚拟交易处理")

            # 根据服务器持仓查询结果决定交易模式
            if is_real_trade:
                # 实际交易模式：检查持仓数量
                if actual_volume < sell_quantity:
                    self.add_record(f"{code} 实际持仓({actual_volume})小于要卖出数量({sell_quantity}),调整卖出数量")
                    sell_quantity = actual_volume
                    if sell_quantity == 0:
                        self.add_record(f"{code} 无可卖出持仓,取消卖出")
                        return

                # 实际交易：发送卖出委托请求
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_SELL,
                        'volume': sell_quantity,
                        'price_type': FIX_PRICE,
                        'price': price - price_adjust
                    }
                }

                # 发送委托请求
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"卖出委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                # 获取委托号
                order_id = response['data']['order_id']
                self.add_record(f"卖出委托已提交 {code} 委托号: {order_id}")

                # 记录卖出委托号到持仓记录
                self.position_records[code]['sell_order_id'] = order_id

                # 添加委托跟踪
                self.order_tracking[order_id] = {
                    'code': code,
                    'last_query_time': datetime.now(),
                    'query_count': 0,
                    'is_completed': False,
                    'order_type': 'sell'
                }

                # 立即查询一次成交状态
                self.query_trade_status(code, order_id)
                
                # 尝试获取实际成交价格（一段时间后再查询）
                def check_sell_trade_report():
                    # 延迟5秒后查询成交回报
                    time.sleep(5)
                    try:
                        # 查询该委托的成交情况
                        real_sell_price = None
                        
                        # 查询当日成交
                        request = {
                            'type': 'query_trades',
                            'params': {
                                'start_date': datetime.now().strftime('%Y%m%d'),
                                'end_date': datetime.now().strftime('%Y%m%d'),
                                'order_id': order_id
                            }
                        }
                        response = self.send_request(request)
                        
                        if response['status'] == 'success' and response['data']:
                            for trade in response['data']:
                                if trade['direction'] == STOCK_SELL and trade['order_id'] == order_id:
                                    # 尝试多种可能的成交价格字段名
                                    real_sell_price = (trade.get('traded_price') or
                                                      trade.get('price') or
                                                      trade.get('deal_price') or
                                                      trade.get('avg_price') or 0)
                                    if real_sell_price > 0:
                                        self.add_record(f"获取到 {code} 的实际卖出价格: {real_sell_price:.3f}")
                                    break
                        
                        # 如果找到实际成交价格，更新交易记录
                        if real_sell_price:
                            for record in self.trade_records:
                                if record.get('sell_order_id') == order_id:
                                    original_price = record['sell_price']
                                    record['sell_price'] = real_sell_price
                                    record['profit'] = round((real_sell_price * sell_quantity) -
                                                          record.get('actual_amount', buy_price * sell_quantity) -
                                                          record.get('total_fee', 0), 2)
                                    record['profit_percent'] = round(record['profit'] /
                                                                  record.get('actual_amount', buy_price * sell_quantity) * 100, 2)
                                    
                                    self.add_record(f"已更新 {code} 的卖出交易记录价格: {original_price:.3f} -> {real_sell_price:.3f}")
                                    self.save_trading_data()
                                    break
                    except Exception as e:
                        self.add_record(f"查询卖出成交回报失败: {str(e)}")
                
                # 启动线程等待并查询成交回报
                threading.Thread(target=check_sell_trade_report, daemon=True).start()
                
            else:
                # 虚拟卖出
                order_id = f"VIRTUAL_SELL_{int(time.time())}"
                self.add_record(f"[虚拟]卖出委托 {code} 价格: {price:.2f}")

            # 计算卖出金额和手续费
            sell_amount = price * sell_quantity
            sell_fee = sell_amount * 0.0001
            total_fee = buy_fee + sell_fee

            # 计算盈亏（基于最早买入价格）
            buy_amount = buy_price * sell_quantity
            profit = sell_amount - buy_amount - total_fee
            profit_percent = (profit / buy_amount) * 100

            # 显示卖出信息 - 基于服务器持仓查询结果显示标签
            virtual_tag = "[虚拟]" if not is_real_trade else ""
            sell_record = f"{virtual_tag}卖出 {code} 价格:{price:.2f} 数量:{sell_quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%) [第1批买入:{buy_price:.2f}×{sell_quantity}]"
            self.add_record(sell_record)

            # 记录单独的卖出操作到交易历史
            sell_trade_record = {
                'code': code,
                'type': 'sell',  # 交易类型：卖出
                'price': price,
                'quantity': sell_quantity,
                'time': time_str,
                'order_id': order_id,
                'fee': sell_fee,
                'amount': price * sell_quantity,
                'virtual': not is_real_trade,
                'timestamp': datetime.now().isoformat()
            }

            # 追加单独的卖出记录到永久交易历史文件
            self.append_trade_to_permanent_history(sell_trade_record)

            # 记录完整交易配对信息到交易历史（保持向后兼容）
            complete_trade_record = {
                'code': code,
                'type': 'complete_trade',  # 标记为完整交易配对
                'buy_price': buy_price,
                'buy_time': buy_time,
                'sell_price': price,
                'sell_time': time_str,
                'quantity': sell_quantity,
                'buy_fee': round(buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': earliest_buy.get('order_id'),
                'sell_order_id': order_id,
                'virtual': not is_real_trade,  # 基于服务器持仓查询结果
                'actual_amount': buy_price * sell_quantity,  # 实际买入金额
                'timestamp': datetime.now().isoformat()
            }

            # 追加完整交易配对记录到永久交易历史文件
            self.append_trade_to_permanent_history(complete_trade_record)

            # 对于实际交易，延迟清除持仓记录，等待成交确认
            if is_real_trade:
                # 标记持仓为待清除状态，等待成交确认后再清除
                position_info['pending_sell'] = {
                    'order_id': order_id,
                    'sell_quantity': sell_quantity,
                    'buy_price': buy_price,
                    'timestamp': datetime.now().isoformat()
                }
                self.add_record(f"{code} 卖出委托已提交，等待成交确认后清除持仓")
            else:
                # 虚拟交易立即清除持仓记录
                # 更新持仓队列 - 移除已完全卖出的买入记录
                buy_queue.pop(0)

                # 更新总计数据
                position_info['total_quantity'] -= sell_quantity
                position_info['total_cost'] -= buy_price * sell_quantity

                # 如果没有剩余持仓，删除整个记录
                if position_info['total_quantity'] == 0 or len(buy_queue) == 0:
                    del self.position_records[code]
                    self.add_record(f"{code} 所有持仓已清空")

                # 移除等待盈利卖出标记
                if hasattr(self, 'waiting_for_profit_sell') and code in self.waiting_for_profit_sell:
                    del self.waiting_for_profit_sell[code]
            
            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
            # 更新股票档案
            if code in self.stock_profiles:
                # 计算手续费
                fee = price * sell_quantity * 0.0001

                # 添加卖出记录
                self.stock_profiles[code].add_trade('sell', price, sell_quantity, time_str, fee, order_id)

                # 保存档案
                self.save_stock_profiles()
            
        except Exception as e:
            self.add_record(f"卖出 {code} 失败: {str(e)}")
    
    def execute_protective_sell(self, code, price, time_str, avg_price):
        """执行保护性卖出操作"""
        try:
            if code not in self.position_records:
                return

            # 获取持仓信息
            buy_info = self.position_records[code]
            quantity = buy_info.get('quantity', 100)

            # 根据股票类型调整卖出价格降价幅度
            if code.startswith(('15', '5')):  # ETF
                price_adjust = 0.004
            else:  # 可转债
                price_adjust = 0.40

            # 智能判断交易模式：无论本地记录如何，都先查询服务器持仓
            is_real_trade = False
            actual_volume = 0

            if self.trading_enabled and not self.is_virtual_trading():
                # 查询服务器实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)

                if response['status'] == 'success':
                    # 检查服务器是否有该股票的持仓
                    for position in response['data']:
                        if position['stock_code'] == code:
                            is_real_trade = True
                            actual_volume = position['volume']
                            self.add_record(f"服务器确认 {code} 实际持仓 {actual_volume} 股，按实际交易处理保护性卖出")
                            break

                    if not is_real_trade:
                        self.add_record(f"服务器未找到 {code} 的实际持仓，按虚拟交易处理保护性卖出")
                else:
                    self.add_record(f"查询服务器持仓失败: {response.get('message', '未知错误')}，按虚拟交易处理保护性卖出")

            # 根据服务器持仓查询结果决定交易模式
            if is_real_trade:
                # 实际交易模式：检查持仓数量
                if actual_volume < quantity:
                    self.add_record(f"{code} 实际持仓({actual_volume})小于记录持仓({quantity}),调整保护性卖出数量")
                    quantity = actual_volume
                    if quantity == 0:
                        self.add_record(f"{code} 无可卖出持仓,取消保护性卖出")
                        # 从持仓记录中移除
                        if code in self.position_records:
                            del self.position_records[code]
                        self.update_position_list()
                        self.save_trading_data()
                        return
            else:
                # 虚拟交易模式，直接使用本地记录的数量
                actual_volume = quantity

            # 获取持仓信息
            buy_price = buy_info['buy_price']
            buy_fee = buy_info.get('fee', 0)

            # 根据交易模式执行不同的卖出逻辑
            if is_real_trade:
                # 实盘交易：发送卖出委托请求
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': code,
                        'direction': STOCK_SELL,  # 卖出指令
                        'volume': quantity,  # 卖出数量
                        'price_type': FIX_PRICE,  # 限价委托
                        'price': price - price_adjust  # 卖出价格降低提高成交概率
                    }
                }

                # 发送委托请求
                response = self.send_request(request)
                if response['status'] != 'success':
                    self.add_record(f"保护性卖出委托失败 {code}: {response.get('message', '未知错误')}")
                    return

                # 获取委托号
                order_id = response['data']['order_id']
                self.add_record(f"保护性卖出委托已提交 {code} 委托号: {order_id}")

                # 记录卖出委托号到持仓记录
                self.position_records[code]['sell_order_id'] = order_id

                # 添加委托跟踪
                self.order_tracking[order_id] = {
                    'code': code,
                    'last_query_time': datetime.now(),
                    'query_count': 0,
                    'is_completed': False,
                    'order_type': 'protective_sell'
                }

                # 立即查询一次成交状态
                self.query_trade_status(code, order_id)
            else:
                # 虚拟交易：直接模拟卖出
                order_id = f"VIRTUAL_PROTECTIVE_SELL_{int(time.time())}"
                self.add_record(f"[虚拟]保护性卖出委托 {code} 价格: {price:.2f}")
            
            # 计算卖出金额和手续费
            sell_amount = price * quantity
            sell_fee = sell_amount * 0.0001
            total_fee = buy_fee + sell_fee
            
            # 计算盈亏
            profit = sell_amount - buy_info.get('actual_amount', buy_price * quantity) - total_fee
            profit_percent = (profit / buy_info.get('actual_amount', buy_price * quantity)) * 100
            
            # 显示卖出信息（包含保护性卖出原因）
            virtual_tag = "[虚拟]" if not is_real_trade else ""
            sell_record = (f"{virtual_tag}卖出 {code} 价格:{price:.2f} 数量:{quantity} 盈亏:{profit:.2f}元({profit_percent:.2f}%) "
                          f"[保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}]")
            self.add_record(sell_record)

            # 记录单独的保护性卖出操作到交易历史
            sell_trade_record = {
                'code': code,
                'type': 'sell',  # 交易类型：卖出
                'price': price,
                'quantity': quantity,
                'time': time_str,
                'order_id': order_id,
                'fee': sell_fee,
                'amount': price * quantity,
                'virtual': not is_real_trade,
                'reason': f"保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}",
                'timestamp': datetime.now().isoformat()
            }

            # 追加单独的卖出记录到永久交易历史文件
            self.append_trade_to_permanent_history(sell_trade_record)

            # 记录完整保护性交易信息到交易历史（保持向后兼容）
            complete_trade_record = {
                'code': code,
                'type': 'complete_trade',  # 标记为完整交易配对
                'buy_price': buy_price,
                'buy_time': buy_info['buy_time'],
                'sell_price': price,
                'sell_time': time_str,
                'quantity': quantity,
                'buy_fee': round(buy_fee, 2),
                'sell_fee': round(sell_fee, 2),
                'total_fee': round(total_fee, 2),
                'profit': round(profit, 2),
                'profit_percent': round(profit_percent, 2),
                'buy_order_id': buy_info.get('order_id'),
                'sell_order_id': order_id,
                'reason': f"保护性卖出: 价格{price:.2f} < 均价{avg_price:.2f}",
                'virtual': not is_real_trade,
                'timestamp': datetime.now().isoformat()
            }

            # 追加完整交易配对记录到永久交易历史文件
            self.append_trade_to_permanent_history(complete_trade_record)

            # 虚拟交易直接从持仓记录中移除，实盘交易等待成交回报
            if not is_real_trade:
                # 从持仓记录中移除
                del self.position_records[code]

            # 更新界面和保存数据
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
            # 更新股票档案
            if code in self.stock_profiles:
                # 计算手续费
                fee = price * quantity * 0.0001
                
                # 添加卖出记录
                self.stock_profiles[code].add_trade('sell', price, quantity, time_str, fee, order_id)
                
                # 保存档案
                self.save_stock_profiles()
            
        except Exception as e:
            self.add_record(f"保护性卖出 {code} 失败: {str(e)}")
    
    def add_record(self, text):
        """添加记录到记录列表并写入日志文件"""
        try:
            # 添加到记录列表
            if hasattr(self, 'record_listbox') and hasattr(self, 'root'):
                try:
                    # 检查根窗口是否仍然存在
                    if self.root.winfo_exists():
                        self._add_record_impl(text)
                except tk.TclError:
                    # GUI已经销毁，只记录到控制台
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    print(f"[{timestamp}] {text}")
            else:
                print(f"记录列表未初始化: {text}")

            # 写入日志文件
            try:
                if hasattr(self, 'log_file') and self.log_file:
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    log_text = f"[{timestamp}] {text}\n"
                    with open(self.log_file, 'a', encoding='utf-8') as f:
                        f.write(log_text)
            except Exception as e:
                print(f"写入日志失败: {str(e)}")

        except Exception as e:
            # 如果是GUI组件已销毁的错误，不打印错误信息
            if "invalid command name" not in str(e):
                print(f"添加记录失败: {str(e)}")
    
    def _add_record_impl(self, text):
        """实际添加记录的实现"""
        try:
            # 检查GUI组件是否仍然有效
            if not hasattr(self, 'record_listbox') or not self.record_listbox.winfo_exists():
                return

            # 添加时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            text_with_time = f"{timestamp} {text}"

            # 添加到记录列表框
            self.record_listbox.insert(0, text_with_time)  # 新记录添加到顶部
        except Exception as e:
            # 如果是GUI组件已销毁的错误，不打印错误信息
            if "invalid command name" not in str(e):
                print(f"添加记录失败: {str(e)}")
    
    def sort_position_column(self, column):
        """对持仓表格按指定列排序"""
        try:
            # 获取所有数据
            data = []
            for item in self.position_tree.get_children():
                values = self.position_tree.item(item)['values']
                data.append((item, values))

            # 确定排序方向
            if self.position_sort_column == column:
                self.position_sort_reverse = not self.position_sort_reverse
            else:
                self.position_sort_column = column
                self.position_sort_reverse = False

            # 根据列类型进行排序
            column_index = ['代码', '数量', '买入价', '现价', '盈亏金额', '盈亏比例', '市值'].index(column)

            if column == '代码':
                # 按字符串排序
                data.sort(key=lambda x: x[1][column_index], reverse=self.position_sort_reverse)
            else:
                # 按数值排序，处理可能的格式问题
                def get_numeric_value(value):
                    try:
                        # 移除可能的符号和百分号
                        if isinstance(value, str):
                            value = value.replace(',', '').replace('%', '').replace('+', '')
                        return float(value)
                    except:
                        return 0

                data.sort(key=lambda x: get_numeric_value(x[1][column_index]), reverse=self.position_sort_reverse)

            # 重新排列TreeView中的项目
            for index, (item, values) in enumerate(data):
                self.position_tree.move(item, '', index)

            # 更新列标题显示排序方向
            position_columns = ['代码', '数量', '买入价', '现价', '盈亏金额', '盈亏比例', '市值']
            for col in position_columns:
                if col == column:
                    direction = " ↓" if self.position_sort_reverse else " ↑"
                    self.position_tree.heading(col, text=col + direction, command=lambda c=col: self.sort_position_column(c))
                else:
                    self.position_tree.heading(col, text=col, command=lambda c=col: self.sort_position_column(c))

        except Exception as e:
            self.add_record(f"排序持仓列表失败: {str(e)}")

    def add_trade_record(self, text):
        """添加交易记录"""
        # 添加到通用记录
        self.add_record(text)
    
    def show_position_stock(self, event):
        """显示选中持仓股票的分时图"""
        selection = self.position_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓股票")
            return

        # 从TreeView中获取选中项的股票代码
        item = selection[0]
        values = self.position_tree.item(item)['values']
        code = values[0]  # 第一列是股票代码

        # 调用显示分时图的方法
        threading.Thread(target=lambda: self.check_and_show_chart(code)).start()
    
    def check_and_show_chart(self, code):
        """检查股票并显示分时图"""
        try:
            # 检查股票代码是否有效
            is_valid = self.is_valid_for_trading(code)
            if not is_valid:
                # 使用after方法在主线程中显示消息
                self.root.after(0, lambda: messagebox.showinfo("提示", f"{code} 当前不可交易或数据不可用"))
                return
            
            # 获取数据
            today = datetime.now().strftime('%Y%m%d')
            self.downloader.my_download([code], '1m', today, today)
            time.sleep(1)  # 等待数据就绪
            
            # 在主线程中显示图表
            self.root.after(0, lambda: self.display_chart(code))
        except Exception as e:
            error_msg = str(e)
            # 使用after方法在主线程中显示错误
            self.root.after(0, lambda: messagebox.showerror("错误", f"准备显示分时图失败: {error_msg}"))
    
    def update_position_list(self):
        """更新持仓列表显示"""
        try:
            # 清空TreeView
            for item in self.position_tree.get_children():
                self.position_tree.delete(item)

            # 初始化当前持仓计数和统计数据
            current_positions = 0
            total_cost = 0  # 持仓总成本
            total_market_value = 0  # 当前市值
            
            if self.trading_enabled and not self.is_virtual_trading():
                # 查询实际持仓
                request = {'type': 'query_positions'}
                response = self.send_request(request)

                if response['status'] != 'success':
                    self.add_record(f"查询持仓失败: {response.get('message', '未知错误')}")
                    # 查询失败时，显示本地持仓记录中的实际交易持仓
                    self._display_local_real_positions()
                    return
                
                # 创建一个集合来跟踪服务器返回的持仓代码
                server_position_codes = set()

                # 遍历服务器返回的持仓数据
                for position in response['data']:
                    code = position.get('stock_code', position.get('code', ''))
                    volume = position.get('volume', 0)

                    # 只处理可转债(11或12开头)和ETF(15或5开头)
                    if not (code.startswith(('11', '12', '15', '5'))):
                        continue

                    if volume <= 0:  # 跳过零持仓
                        continue

                    # 添加到服务器持仓代码集合
                    server_position_codes.add(code)

                    current_positions += 1  # 增加持仓计数

                    # 优先使用服务器返回的价格和盈亏信息
                    server_cost_price = (position.get('cost_price') or
                                        position.get('avg_price') or
                                        position.get('average_price') or
                                        position.get('buy_price'))
                    server_current_price = (position.get('current_price') or
                                          position.get('last_price') or
                                          position.get('price') or
                                          position.get('market_price'))
                    server_profit = (position.get('profit') or
                                   position.get('pnl') or
                                   position.get('unrealized_pnl') or
                                   position.get('float_profit'))
                    server_market_value = position.get('market_value')

                    # 如果服务器没有返回当前价格，则获取实时价格
                    current_price = server_current_price if server_current_price else self.get_current_price(code)
                    if not current_price:
                        continue

                    # 从服务器数据或本地记录获取买入信息
                    buy_info = self.position_records.get(code, {})

                    # 如果服务器返回了成本价，优先使用服务器数据
                    if server_cost_price:
                        buy_price = server_cost_price

                        # 如果服务器没有直接返回盈亏，但有市值，则计算盈亏
                        if server_profit is None and server_market_value:
                            cost_value = server_cost_price * volume
                            server_profit = server_market_value - cost_value
                            #self.add_record(f"[{code}] 服务器计算盈亏: 市值{server_market_value:.2f} - 成本{cost_value:.2f} = {server_profit:.2f}")

                        # 更新或创建持仓记录，使用服务器数据 - 使用新格式
                        buy_record = {
                            'buy_price': server_cost_price,
                            'buy_time': buy_info.get('buy_time', datetime.now().strftime('%H:%M:%S')),
                            'quantity': volume,
                            'fee': buy_info.get('fee', server_cost_price * volume * 0.0001),
                            'actual_amount': server_cost_price * volume,
                            'order_id': f"SERVER_SYNC_{int(time.time())}",
                            'virtual': False,
                            'below_exp3_at_buy': False,
                            'crossed_exp3': False,
                            'real_trade': True,  # 标记为实际交易持仓
                            'server_profit': server_profit,  # 保存服务器计算的盈亏
                            'server_market_value': server_market_value,  # 保存服务器市值
                            'server_current_price': server_current_price,  # 保存服务器当前价
                            'use_server_data': True  # 标记使用服务器数据
                        }

                        new_position_info = {
                            'buy_queue': [buy_record],
                            'total_quantity': volume,
                            'total_cost': server_cost_price * volume,
                            'total_fee': buy_info.get('fee', server_cost_price * volume * 0.0001)
                        }
                        self.position_records[code] = new_position_info

                    else:
                        # 服务器没有返回成本价，使用本地记录或创建新记录
                        if not buy_info:
                            # 尝试从成交回报获取真实成交价格
                            self.add_record(f"发现新持仓 {code}，尝试从成交回报获取实际成交价格")

                            # 创建一个新的持仓记录 - 使用新格式
                            buy_record = {
                                'buy_price': current_price,  # 临时使用当前价格，后续会尝试更新
                                'buy_time': datetime.now().strftime('%H:%M:%S'),
                                'quantity': volume,
                                'fee': current_price * volume * 0.0001,  # 手续费万分之一
                                'actual_amount': current_price * volume,
                                'order_id': f"NEW_POSITION_{int(time.time())}",
                                'virtual': False,
                                'below_exp3_at_buy': False,
                                'crossed_exp3': False,
                                'real_trade': True  # 标记为实际交易持仓
                            }

                            new_position_info = {
                                'buy_queue': [buy_record],
                                'total_quantity': volume,
                                'total_cost': current_price * volume,
                                'total_fee': current_price * volume * 0.0001
                            }

                            # 添加到持仓记录
                            self.position_records[code] = new_position_info
                        else:
                            # 更新现有记录的持仓量（可能已经发生部分卖出）
                            # 检查是否是新格式
                            if 'buy_queue' in buy_info:
                                # 新格式：更新总数量
                                buy_info['total_quantity'] = volume
                                # 确保标记为实际交易持仓
                                if buy_info['buy_queue']:
                                    buy_info['buy_queue'][0]['real_trade'] = True
                                buy_price = buy_info['total_cost'] / buy_info['total_quantity'] if buy_info['total_quantity'] > 0 else current_price
                            else:
                                # 旧格式：转换为新格式
                                old_buy_price = buy_info.get('buy_price', current_price)
                                buy_record = {
                                    'buy_price': old_buy_price,
                                    'buy_time': buy_info.get('buy_time', datetime.now().strftime('%H:%M:%S')),
                                    'quantity': volume,
                                    'fee': buy_info.get('fee', old_buy_price * volume * 0.0001),
                                    'actual_amount': old_buy_price * volume,
                                    'order_id': buy_info.get('order_id', f"CONVERTED_{int(time.time())}"),
                                    'virtual': buy_info.get('virtual', False),
                                    'below_exp3_at_buy': buy_info.get('below_exp3_at_buy', False),
                                    'crossed_exp3': buy_info.get('crossed_exp3', False),
                                    'real_trade': True
                                }

                                new_buy_info = {
                                    'buy_queue': [buy_record],
                                    'total_quantity': volume,
                                    'total_cost': old_buy_price * volume,
                                    'total_fee': buy_info.get('fee', old_buy_price * volume * 0.0001)
                                }

                                # 保留其他字段
                                for key, value in buy_info.items():
                                    if key not in ['buy_price', 'quantity', 'buy_time', 'fee', 'actual_amount', 'virtual', 'order_id', 'below_exp3_at_buy', 'crossed_exp3']:
                                        new_buy_info[key] = value

                                buy_info = new_buy_info
                                buy_price = old_buy_price

                            # 更新本地持仓记录
                            self.position_records[code] = buy_info

                    # 计算成本和市值 - 优先使用服务器数据保持与表格显示一致
                    if buy_price is not None and current_price is not None:
                        # 优先使用服务器返回的市值和成本价数据
                        if server_market_value and server_cost_price:
                            # 使用服务器数据
                            position_cost = server_cost_price * volume
                            position_market_value = server_market_value
                        else:
                            # 使用本地计算
                            position_cost = buy_price * volume
                            position_market_value = current_price * volume

                        total_cost += position_cost
                        total_market_value += position_market_value
                    else:
                        # 如果价格数据无效，跳过此持仓的统计计算
                        continue

                    # 显示持仓信息，传递完整的服务器数据
                    server_data = {
                        'profit': server_profit,
                        'market_value': server_market_value,
                        'current_price': server_current_price,
                        'cost_price': server_cost_price
                    }
                    self.display_position(code, volume, current_price, buy_info, server_data=server_data)

                # 移除服务器上不存在的持仓（已卖出但本地记录未更新的情况）
                for code in list(self.position_records.keys()):
                    if code not in server_position_codes and self.position_records[code].get('real_trade', False):
                        self.add_record(f"服务器上不存在持仓 {code}，从本地记录中移除")
                        del self.position_records[code]
                
            else:
                # 显示虚拟持仓
                for code, info in self.position_records.items():
                    # 检查是否为虚拟持仓
                    is_virtual = False
                    if 'buy_queue' in info and info['buy_queue']:
                        # 新格式：检查队列中的第一个元素
                        is_virtual = info['buy_queue'][0].get('virtual', False)
                    else:
                        # 旧格式：检查顶层字段
                        is_virtual = info.get('virtual', False)

                    if is_virtual:  # 只显示虚拟持仓
                        current_positions += 1  # 增加持仓计数
                        volume = info.get('quantity', 0)
                        if 'buy_queue' in info and info['buy_queue']:
                            volume = info['total_quantity']
                        current_price = self.get_current_price(code)

                        if current_price:
                            # 计算成本和市值
                            if 'buy_queue' in info and info['buy_queue']:
                                buy_price = info['buy_queue'][0]['buy_price']
                            else:
                                buy_price = info.get('buy_price', current_price)
                            if buy_price is not None and current_price is not None:
                                position_cost = buy_price * volume
                                position_market_value = current_price * volume

                                total_cost += position_cost
                                total_market_value += position_market_value

                        self.display_position(code, volume, current_price, info, virtual=True, server_data=None)
            
            # 计算浮动盈亏：当前市值 - 总持仓成本
            # 确保数值有效性，避免None值参与计算
            if total_market_value is not None and total_cost is not None:
                floating_profit = total_market_value - total_cost
            else:
                floating_profit = 0
                # 如果数据无效，使用默认值
                if total_market_value is None:
                    total_market_value = 0
                if total_cost is None:
                    total_cost = 0

            # 更新持仓统计显示，包含总成本、当前市值和浮动盈亏
            if floating_profit > 0:
                floating_text = f"+{floating_profit:,.2f}"
            else:
                floating_text = f"{floating_profit:,.2f}"

            stats_text = (
                f"当前持仓: {current_positions} | "
                f"持仓总成本: {total_cost:,.2f} | "
                f"当前市值: {total_market_value:,.2f} | "
                f"浮动盈亏: {floating_text}"
            )
            self.position_stats_label.config(text=stats_text)
            
            # 保存更新后的持仓数据
            self.save_trading_data()
            
            # 更新交易汇总
            self.update_trade_summary()
            
        except Exception as e:
            self.add_record(f"更新持仓列表失败: {str(e)}")
            print(f"更新持仓列表失败: {str(e)}")  # 添加控制台输出以便调试

    def _display_local_real_positions(self):
        """显示本地记录中的实际交易持仓（当服务器查询失败时使用）"""
        try:
            current_positions = 0
            total_cost = 0
            total_market_value = 0

            for code, info in self.position_records.items():
                # 检查是否为实际交易持仓
                is_real_trade = False
                if 'buy_queue' in info and info['buy_queue']:
                    # 新格式：检查队列中的第一个元素
                    first_buy = info['buy_queue'][0]
                    is_real_trade = not first_buy.get('virtual', False) or first_buy.get('real_trade', False)
                else:
                    # 旧格式：检查顶层字段
                    is_real_trade = not info.get('virtual', False)

                if is_real_trade:
                    current_positions += 1

                    # 获取持仓数量
                    if 'buy_queue' in info and info['buy_queue']:
                        volume = info['total_quantity']
                        buy_price = info['buy_queue'][0]['buy_price']
                    else:
                        volume = info.get('quantity', 0)
                        buy_price = info.get('buy_price', 0)

                    # 获取当前价格
                    current_price = self.get_current_price(code)

                    if current_price and buy_price:
                        position_cost = buy_price * volume
                        position_market_value = current_price * volume
                        total_cost += position_cost
                        total_market_value += position_market_value

                    # 显示持仓
                    self.display_position(code, volume, current_price, info, virtual=False, server_data=None)

            # 计算浮动盈亏
            floating_profit = total_market_value - total_cost if total_market_value and total_cost else 0

            # 更新统计显示
            if floating_profit > 0:
                floating_text = f"+{floating_profit:,.2f}"
            else:
                floating_text = f"{floating_profit:,.2f}"

            stats_text = (
                f"当前持仓: {current_positions} | "
                f"持仓总成本: {total_cost:,.2f} | "
                f"当前市值: {total_market_value:,.2f} | "
                f"浮动盈亏: {floating_text}"
            )
            self.position_stats_label.config(text=stats_text)

            # 保存更新后的持仓数据
            self.save_trading_data()

            # 更新交易汇总
            self.update_trade_summary()

        except Exception as e:
            self.add_record(f"显示本地实际交易持仓失败: {str(e)}")
    
    def display_position(self, code, volume, current_price, buy_info, virtual=False, server_profit=None, server_data=None):
        """显示持仓信息到TreeView表格"""
        if not buy_info:
            return

        # 检查是否是新的队列结构
        if 'buy_queue' in buy_info:
            buy_queue = buy_info['buy_queue']
            if not buy_queue:
                return

            # 使用最早的买入记录进行显示
            earliest_buy = buy_queue[0]
            buy_price = earliest_buy['buy_price']
            total_volume = buy_info['total_quantity']
        else:
            # 兼容旧的结构
            buy_price = buy_info.get('buy_price', buy_info.get('price', 0))
            total_volume = volume

        # 检查价格数据有效性
        if current_price is None or buy_price is None:
            # 如果价格数据无效，显示基本信息
            values = (code, total_volume, "N/A", "N/A", "N/A", "N/A", "N/A")
            self.position_tree.insert('', 'end', values=values)
            return

        # 优先使用服务器数据（如果在实盘交易模式下）
        use_server_data = False
        data_source = "本地"

        if not virtual and server_data:
            # 实盘交易模式下，优先使用服务器数据
            server_market_value = server_data.get('market_value')
            server_cost_price = server_data.get('cost_price')
            server_current_price = server_data.get('current_price')
            server_profit = server_data.get('profit')

            if server_market_value and server_cost_price:
                # 使用服务器市值和成本价计算盈亏
                cost_value = server_cost_price * total_volume
                total_profit = server_market_value - cost_value
                total_profit_percent = (total_profit / cost_value) * 100 if cost_value > 0 else 0
                market_value = server_market_value
                use_server_data = True
                data_source = "服务器"

                # 5分钟间隔的调试信息
                if not hasattr(self, '_last_debug_time'):
                    self._last_debug_time = {}

                current_time = time.time()
                last_log_time = self._last_debug_time.get(code, 0)

                if current_time - last_log_time >= 300:  # 5分钟 = 300秒
                    self._last_debug_time[code] = current_time
                    #self.add_record(f"[{code}] 服务器计算盈亏: 市值{server_market_value:.2f} - 成本{cost_value:.2f} = {total_profit:.2f}")

            elif server_profit is not None:
                # 直接使用服务器返回的盈亏
                total_profit = server_profit
                total_profit_percent = (total_profit / (buy_price * total_volume)) * 100 if buy_price * total_volume > 0 else 0
                market_value = current_price * total_volume
                use_server_data = True
                data_source = "服务器"

            else:
                # 服务器数据不完整，使用本地计算
                profit = (current_price - buy_price) * total_volume
                buy_fee = buy_price * total_volume * 0.0003
                sell_fee = current_price * total_volume * 0.0003
                total_fee = buy_fee + sell_fee
                total_profit = profit - total_fee
                total_profit_percent = total_profit / (buy_price * total_volume) * 100
                market_value = current_price * total_volume
                data_source = "本地(服务器数据不完整)"
        else:
            # 虚拟交易或没有服务器数据，使用本地计算
            profit = (current_price - buy_price) * total_volume
            buy_fee = buy_price * total_volume * 0.0003
            sell_fee = current_price * total_volume * 0.0003
            total_fee = buy_fee + sell_fee
            total_profit = profit - total_fee
            total_profit_percent = total_profit / (buy_price * total_volume) * 100
            market_value = current_price * total_volume
            if virtual:
                data_source = "虚拟"


        # 市值在上面的逻辑中已经计算，这里不需要重复计算
        # market_value 已在上面的条件分支中设置

        # 格式化数据，添加数据来源标识
        profit_color = "red" if total_profit < 0 else "green"

        # 根据数据来源添加标识
        if data_source == "服务器":
            profit_text = f"[服务器]{total_profit:+.2f}"
        elif data_source == "虚拟":
            profit_text = f"[虚拟]{total_profit:+.2f}"
        elif "服务器数据不完整" in data_source:
            profit_text = f"[本地*]{total_profit:+.2f}"  # *表示服务器数据不完整
        else:
            profit_text = f"[本地]{total_profit:+.2f}"

        profit_percent_text = f"{total_profit_percent:+.2f}%"

        # 插入到TreeView
        values = (
            code,
            total_volume,
            f"{buy_price:.3f}",
            f"{current_price:.3f}",
            profit_text,
            profit_percent_text,
            f"{market_value:.2f}"
        )

        item = self.position_tree.insert('', 'end', values=values)

        # 设置盈亏颜色
        if total_profit < 0:
            self.position_tree.set(item, '盈亏金额', profit_text)
            self.position_tree.set(item, '盈亏比例', profit_percent_text)
            # 注意：TreeView的颜色设置需要通过tags实现
            self.position_tree.item(item, tags=('loss',))
        else:
            self.position_tree.item(item, tags=('profit',))

        # 配置颜色标签（如果还没有配置）
        if not hasattr(self, '_position_colors_configured'):
            self.position_tree.tag_configure('profit', foreground='red')    # 盈利设为红色
            self.position_tree.tag_configure('loss', foreground='green')    # 亏损设为绿色
            self._position_colors_configured = True
    
    def get_current_price(self, code, return_time=False, use_realtime=True):
        """获取股票当前价格
        Args:
            code: 股票代码
            return_time: 是否同时返回价格对应的时间
            use_realtime: 是否优先使用实时价格（交易时段内）
        Returns:
            如果return_time=False: 返回价格(float)或None
            如果return_time=True: 返回(价格, 时间)元组或(None, None)
        """
        try:
            # 获取当前时间
            current_time = datetime.now()
            current_hour = current_time.hour
            current_minute = current_time.minute
            today = current_time.strftime('%Y%m%d')

            # 在交易时段内且启用实时价格时，优先尝试获取1分钟数据
            if use_realtime and self.is_trading_time():
                try:
                    # 尝试获取最新的1分钟数据
                    realtime_data = xtdata.get_market_data(
                        field_list=['time', 'close'],
                        stock_list=[code],
                        period='1m',
                        start_time=today,
                        end_time=today,
                        count=10  # 获取最近10个1分钟数据点
                    )

                    if realtime_data and code in realtime_data['time'].index:
                        # 创建DataFrame
                        df_realtime = pd.DataFrame({
                            'time': pd.to_datetime(realtime_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.tz_localize(None),
                            'close': realtime_data['close'].loc[code]
                        })

                        # 过滤交易时间范围
                        df_realtime = df_realtime[
                            (df_realtime['time'].dt.time >= pd.Timestamp('09:30:00').time()) &
                            (df_realtime['time'].dt.time <= pd.Timestamp('15:00:00').time()) &
                            (df_realtime['time'].dt.date <= current_time.date())
                        ]

                        if not df_realtime.empty:
                            # 获取最新的实时价格
                            latest_price = round(df_realtime['close'].iloc[-1], 3)
                            latest_time = df_realtime['time'].iloc[-1]

                            # 检查数据是否足够新（5分钟内）
                            time_diff = current_time - latest_time.replace(tzinfo=None)
                            if time_diff.total_seconds() <= 300:  # 5分钟内的数据认为是实时的
                                if return_time:
                                    return latest_price, latest_time
                                else:
                                    return latest_price
                except Exception:
                    pass  # 实时数据获取失败，继续使用30分钟数据

            # 获取数据的时间范围
            end_time = today
            start_time = (current_time - timedelta(days=5)).strftime('%Y%m%d')  # 获取5天的数据

            # 先从服务器下载最新数据
            try:
                xtdata.download_history_data(
                    code,
                    period='30m',
                    start_time=start_time,
                    end_time=end_time,
                    incrementally=False
                )
            except Exception as download_error:
                pass  # 静默处理下载错误

            # 获取30分钟K线数据
            minute_data = xtdata.get_market_data(
                field_list=['time', 'close'],
                stock_list=[code],
                period='30m',
                start_time=start_time,
                end_time=end_time,
                count=-1
            )

            # 检查数据是否有效
            if not minute_data or code not in minute_data['time'].index:
                return (None, None) if return_time else None

            # 创建DataFrame
            df = pd.DataFrame({
                'time': pd.to_datetime(minute_data['time'].loc[code], unit='ms').dt.tz_localize('UTC').dt.tz_convert('Asia/Shanghai').dt.tz_localize(None),
                'close': minute_data['close'].loc[code]
            })

            # 过滤交易时间范围和当前日期
            current_date = current_time.date()
            df = df[
                (df['time'].dt.time >= pd.Timestamp('09:30:00').time()) &
                (df['time'].dt.time <= pd.Timestamp('15:00:00').time()) &
                (df['time'].dt.date <= current_date)
            ]

            if df.empty:
                return (None, None) if return_time else None

            # 获取最新的价格和时间
            latest_price = round(df['close'].iloc[-1], 3)
            latest_time = df['time'].iloc[-1]

            if return_time:
                return latest_price, latest_time
            else:
                return latest_price

        except Exception as e:
            return (None, None) if return_time else None
    
    def update_trade_summary(self):
        """更新交易汇总信息"""
        # 分别统计不同类型的交易记录
        if self.trade_records:
            # 只统计完整交易配对记录（保持向后兼容）
            complete_trades = [record for record in self.trade_records
                             if record.get('type') == 'complete_trade' or 'profit' in record]

            # 统计单独的买入和卖出操作
            buy_trades = [record for record in self.trade_records if record.get('type') == 'buy']
            sell_trades = [record for record in self.trade_records if record.get('type') == 'sell']

            # 完整交易配对统计
            daily_total_trades = len(complete_trades)
            daily_profit_trades = sum(1 for record in complete_trades if record.get('profit', 0) > 0)
            daily_loss_trades = sum(1 for record in complete_trades if record.get('profit', 0) <= 0)
            daily_total_profit = sum(record.get('profit', 0) for record in complete_trades)
            daily_total_fee = sum(record.get('total_fee', 0) for record in complete_trades)

            # 单独操作统计
            daily_buy_count = len(buy_trades)
            daily_sell_count = len(sell_trades)
            daily_buy_amount = sum(record.get('amount', 0) for record in buy_trades)
            daily_sell_amount = sum(record.get('amount', 0) for record in sell_trades)
        else:
            daily_total_trades = daily_profit_trades = daily_loss_trades = 0
            daily_total_profit = daily_total_fee = 0
            daily_buy_count = daily_sell_count = 0
            daily_buy_amount = daily_sell_amount = 0

        # 计算所有股票档案的历史汇总（包含连续性数据）
        profile_total_trades = 0
        profile_profit_trades = 0
        profile_loss_trades = 0
        profile_total_profit = 0.0
        profile_total_fee = 0.0

        for code, profile in self.stock_profiles.items():
            summary = profile.get_summary()
            profile_total_trades += summary['trade_count']
            profile_profit_trades += summary['win_count']
            profile_loss_trades += summary['loss_count']
            profile_total_profit += summary['total_profit']
            profile_total_fee += summary['total_fee']

        # 计算当前持仓的浮动盈亏
        current_position_profit = 0.0
        current_position_count = 0

        for code, position_info in self.position_records.items():
            try:
                # 获取当前价格
                current_price = self.get_current_price(code)
                if current_price is None:
                    continue

                # 计算持仓盈亏
                if 'buy_queue' in position_info and position_info['buy_queue']:
                    # 新格式：队列结构
                    total_cost = sum(buy['actual_amount'] for buy in position_info['buy_queue'])
                    total_quantity = sum(buy['quantity'] for buy in position_info['buy_queue'])
                    if total_quantity > 0:
                        current_market_value = current_price * total_quantity
                        position_profit = current_market_value - total_cost
                        current_position_profit += position_profit
                        current_position_count += 1
                elif 'buy_price' in position_info and position_info.get('quantity', 0) > 0:
                    # 旧格式：直接结构
                    buy_price = position_info['buy_price']
                    quantity = position_info['quantity']
                    cost = buy_price * quantity
                    current_market_value = current_price * quantity
                    position_profit = current_market_value - cost
                    current_position_profit += position_profit
                    current_position_count += 1
            except Exception as e:
                continue

        # 合并总计（历史交易盈亏 + 当前持仓浮动盈亏）
        # 使用档案数据作为历史交易数据源
        total_trades = profile_total_trades
        profit_trades = profile_profit_trades
        loss_trades = profile_loss_trades
        historical_profit = profile_total_profit  # 历史已实现盈亏
        total_fee = profile_total_fee

        # 如果没有档案数据，则使用当日数据
        if total_trades == 0 and daily_total_trades > 0:
            total_trades = daily_total_trades
            profit_trades = daily_profit_trades
            loss_trades = daily_loss_trades
            historical_profit = daily_total_profit
            total_fee = daily_total_fee

        # 总盈亏 = 历史已实现盈亏 + 当前持仓浮动盈亏
        total_profit = historical_profit + current_position_profit

        # 更新标签
        self.total_trades_label.config(text=f"总交易次数: {total_trades}")
        self.profit_trades_label.config(text=f"盈利交易: {profit_trades}")
        self.loss_trades_label.config(text=f"亏损交易: {loss_trades}")
        self.total_fee_label.config(text=f"总手续费: {total_fee:.2f}元")

        # 更新总盈亏显示（包含历史盈亏和当前持仓浮动盈亏）
        if current_position_count > 0:
            # 有持仓时，显示详细信息
            if total_profit > 0:
                self.total_profit_label.config(text=f"总盈亏: +{total_profit:.2f}元 (已实现:{historical_profit:.2f} 浮动:{current_position_profit:+.2f})")
            else:
                self.total_profit_label.config(text=f"总盈亏: {total_profit:.2f}元 (已实现:{historical_profit:.2f} 浮动:{current_position_profit:+.2f})")
        else:
            # 无持仓时，只显示历史盈亏
            if historical_profit > 0:
                self.total_profit_label.config(text=f"总盈亏: +{historical_profit:.2f}元 (已实现)")
            else:
                self.total_profit_label.config(text=f"总盈亏: {historical_profit:.2f}元 (已实现)")
    
    def import_trading_data(self):
        """从文件导入交易数据"""
        try:
            # 打开文件选择对话框
            position_file = filedialog.askopenfilename(
                title="选择持仓记录文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 加载持仓记录
            with open(position_file, 'r', encoding='utf-8') as f:
                self.position_records = json.load(f)
            
            # 更新持仓列表
            self.update_position_list()
            
            # 提示用户选择交易历史文件
            trade_file = filedialog.askopenfilename(
                title="选择交易历史文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            
            if trade_file:
                # 加载交易历史
                with open(trade_file, 'r', encoding='utf-8') as f:
                    self.trade_records = json.load(f)
                
                # 更新交易汇总
                self.update_trade_summary()
            
            # 保存到当日文件
            self.save_trading_data()
            
            messagebox.showinfo("成功", f"已导入 {len(self.position_records)} 条持仓记录和 {len(self.trade_records)} 条交易历史")
            
        except Exception as e:
            messagebox.showerror("错误", f"导入交易数据失败: {str(e)}")

    def export_trading_data(self):
        """导出交易数据到用户指定文件"""
        try:
            # 选择保存持仓记录的位置
            position_file = filedialog.asksaveasfilename(
                title="保存持仓记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"自选股30m系统持仓记录_{self.today}_导出.json"
            )
            
            if not position_file:
                return  # 用户取消了选择
                
            # 保存持仓记录
            with open(position_file, 'w', encoding='utf-8') as f:
                json.dump(self.position_records, f, ensure_ascii=False, indent=2)
            
            # 选择保存交易历史的位置
            trade_file = filedialog.asksaveasfilename(
                title="保存交易历史",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                initialfile=f"自选股30m系统交易历史_{self.today}_导出.json"
            )
            
            if trade_file:
                # 保存交易历史
                with open(trade_file, 'w', encoding='utf-8') as f:
                    json.dump(self.trade_records, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("成功", "交易数据导出成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"导出交易数据失败: {str(e)}")

    def monitor_orders(self):
        """监控委托状态的线程函数"""
        # 添加委托状态缓存
        order_status_cache = {}
        
        while self.monitoring:
            try:
                # 检查是否在交易时间
                current_time = datetime.now().time()
                if not (('09:30:00' <= current_time.strftime('%H:%M:%S') <= '11:30:00') or 
                       ('13:00:00' <= current_time.strftime('%H:%M:%S') <= '14:57:00')):
                    time.sleep(60)
                    continue
                
                # 如果没有未完成的买卖指令，直接跳过
                if not self.trade_instructions:
                    time.sleep(30)  # 没有委托时延长检查间隔
                    continue
                
                # 获取当天日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 获取需要查询的股票列表
                monitor_codes = list(self.trade_instructions.keys())
                
                # 查询当日委托
                request = {
                    'type': 'query_orders',
                    'params': {
                        'start_date': today,
                        'end_date': today,
                        'stock_codes': monitor_codes
                    }
                }
                response = self.send_request(request)
                
                if response['status'] != 'success':
                    self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                    time.sleep(10)
                    continue
                
                # 检查每个委托的状态
                for order in response['data']:
                    code = order.get('stock_code', '')
                    order_id = order.get('order_id', '')
                    status = order.get('order_status', 0)

                    # 尝试多种可能的成交量字段名，根据官方文档，委托中的成交量字段应该是 traded_volume
                    volume = (order.get('traded_volume') or
                             order.get('volume') or
                             order.get('deal_volume') or
                             order.get('filled_volume') or 0)

                    # 检查必要字段是否有效
                    if not code or not order_id:
                        continue
                    
                    # 检查委托状态是否发生变化
                    cache_key = f"{code}_{order_id}"
                    if cache_key in order_status_cache:
                        cached_status = order_status_cache[cache_key]
                        if cached_status['status'] == status and cached_status['volume'] == volume:
                            continue  # 状态未变化，跳过输出
                    
                    # 更新缓存
                    order_status_cache[cache_key] = {
                        'status': status,
                        'volume': volume
                    }
                    
                    # 输出委托详细信息
                    self.add_record(f"检查委托: {code} 委托号: {order_id} 状态: {status} 成交量: {volume}")
                    
                    # 检查委托号是否匹配
                    if code in self.trade_instructions:
                        instruction = self.trade_instructions[code]
                        if order_id != instruction['order_id']:
                            self.add_record(f"跳过不匹配的委托: {code} 期望: {instruction['order_id']} 实际: {order_id}")
                            continue
                        
                        # 检查委托状态
                        if status in [56, 57]:  # 已成、废单
                            self.add_record(f"委托已完成: {code} 状态: {status}")
                            
                            # 如果已成交，立即更新成交价格
                            if status == 56 and volume > 0:
                                self.process_trade_reports(code, order_id)
                                
                            del self.trade_instructions[code]  # 删除已完成的指令
                            if cache_key in order_status_cache:
                                del order_status_cache[cache_key]  # 删除缓存
                            continue
                        
                        # 检查成交量
                        if volume == 0:
                            # 获取委托时长(秒)
                            order_time_ms = order.get('order_time', 0)
                            if order_time_ms:
                                try:
                                    order_time = datetime.fromtimestamp(order_time_ms/1000)
                                    elapsed_seconds = (datetime.now() - order_time).total_seconds()

                                    # 如果委托超过10秒未成交，执行撤单重试
                                    if elapsed_seconds >= 10:
                                        self.add_record(f"开始重试委托: {code} 委托号: {order_id} 已等待: {elapsed_seconds}秒")
                                        self.retry_failed_order(order)
                                except (ValueError, OSError) as e:
                                    self.add_record(f"解析委托时间失败: {order_time_ms}, 错误: {str(e)}")
                            else:
                                self.add_record(f"委托 {code} 缺少时间信息，跳过重试检查")
                        elif volume > 0 and status == 7:  # 部分成交
                            # 有部分成交，立即处理成交回报
                            self.process_trade_reports(code, order_id)

                # 检查委托跟踪，对未完成的委托进行5分钟定时查询
                self.check_pending_orders()

                # 检查并清理超时的待确认卖出记录
                self.cleanup_timeout_pending_sells()

                time.sleep(30)  # 每30秒检查一次，降低资源占用
                
            except Exception as e:
                self.add_record(f"监控委托出错: {str(e)}")
                time.sleep(10)

    def process_trade_reports(self, code=None, order_id=None):
        """处理成交回报，更新持仓记录中的买入价格

        Args:
            code (str, optional): 股票代码，如果指定则只处理该股票的成交回报
            order_id (str, optional): 委托号，如果指定则只处理该委托号的成交回报
        """
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')

            # 清理过期的成交回报缓存（每天清理一次）
            if not hasattr(self, '_last_cache_clean_date') or self._last_cache_clean_date != today:
                self.processed_trades_cache.clear()
                self._last_cache_clean_date = today
            
            # 查询当日成交
            request = {
                'type': 'query_trades',
                'params': {
                    'start_date': today,
                    'end_date': today
                }
            }
            
            # 如果指定了股票代码，添加到查询参数中
            if code:
                request['params']['stock_codes'] = [code]
                
            # 如果指定了委托号，添加到查询参数中
            if order_id:
                request['params']['order_id'] = order_id
                
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询成交回报失败: {response.get('message', '未知错误')}")
                return
                
            # 处理成交回报数据
            for trade in response['data']:
                trade_code = trade.get('stock_code', '')
                trade_order_id = trade.get('order_id', '')

                # 尝试多种可能的成交量字段名，根据官方文档，成交量字段应该是 traded_volume
                trade_volume = (trade.get('traded_volume') or
                               trade.get('volume') or
                               trade.get('quantity') or
                               trade.get('deal_volume') or 0)

                # 尝试多种可能的成交价格字段名，根据官方文档，成交价格字段应该是 traded_price
                trade_price = (trade.get('traded_price') or
                              trade.get('price') or
                              trade.get('deal_price') or
                              trade.get('avg_price') or 0)

                # 尝试多种可能的方向字段名
                trade_direction = (trade.get('direction') or
                                 trade.get('trade_direction') or
                                 trade.get('side') or
                                 trade.get('order_type') or
                                 trade.get('bs_flag') or 0)

                # 添加调试信息，显示原始数据
                self.add_record(f"[调试] 成交回报原始数据: {trade}")
                self.add_record(f"[调试] 方向字段值: direction={trade.get('direction')}, trade_direction={trade.get('trade_direction')}, side={trade.get('side')}, order_type={trade.get('order_type')}, bs_flag={trade.get('bs_flag')}")

                # 处理交易时间
                trade_time_ms = trade.get('trade_time', 0)
                if trade_time_ms:
                    trade_time = datetime.fromtimestamp(trade_time_ms/1000).strftime('%H:%M:%S')
                else:
                    trade_time = datetime.now().strftime('%H:%M:%S')
                
                # 检查必要字段是否有效
                if not trade_code or not trade_order_id or trade_volume <= 0 or trade_price <= 0:
                    self.add_record(f"跳过无效成交回报: 代码={trade_code}, 委托号={trade_order_id}, 成交量={trade_volume}, 价格={trade_price}")
                    continue

                # 创建成交回报的唯一标识，避免重复显示
                trade_cache_key = f"{trade_order_id}_{trade_time}_{trade_volume}_{trade_price:.3f}"

                # 检查是否已经处理过这个成交回报
                if trade_cache_key in self.processed_trades_cache:
                    continue  # 跳过已处理的成交回报

                # 添加到已处理缓存
                self.processed_trades_cache.add(trade_cache_key)

                # 记录成交详情 - 只在首次处理时显示
                # 修复方向显示问题：支持中文字符串和数字常量两种格式
                if trade_direction == STOCK_BUY or trade_direction == '买入' or trade_direction == 'buy':
                    direction_text = '买入'
                    is_buy_trade = True
                elif trade_direction == STOCK_SELL or trade_direction == '卖出' or trade_direction == 'sell':
                    direction_text = '卖出'
                    is_buy_trade = False
                else:
                    # 对于未知的direction值，添加调试信息
                    direction_text = f'未知方向({trade_direction})'
                    is_buy_trade = None
                    self.add_record(f"[调试] 未知的交易方向值: {trade_direction}, STOCK_BUY={STOCK_BUY}, STOCK_SELL={STOCK_SELL}")

                self.add_record(f"成交回报: {trade_code} 委托号:{trade_order_id} 方向:{direction_text} "
                               f"成交量:{trade_volume} 成交价:{trade_price:.3f} 时间:{trade_time}")

                # 买入成交，更新持仓记录中的买入价格
                if is_buy_trade and trade_code in self.position_records:
                    position_info = self.position_records[trade_code]

                    # 获取原始价格（兼容新旧格式）
                    if 'buy_queue' in position_info and position_info['buy_queue']:
                        original_price = position_info['buy_queue'][0]['buy_price']
                        original_qty = position_info.get('total_quantity', 0)
                        original_amt = position_info.get('total_cost', 0)
                    else:
                        # 旧格式兼容
                        original_price = position_info.get('buy_price', 0)
                        original_qty = position_info.get('quantity', 0)
                        original_amt = position_info.get('actual_amount', 0)
                    
                    # 检查是否是新格式
                    if 'buy_queue' in position_info:
                        # 新格式：更新buy_queue中的记录
                        if position_info['buy_queue']:
                            # 更新第一个买入记录的价格（通常是成交回报更新）
                            buy_record = position_info['buy_queue'][0]
                            buy_record['buy_price'] = trade_price
                            buy_record['buy_time'] = trade_time
                            buy_record['quantity'] = trade_volume
                            buy_record['actual_amount'] = trade_price * trade_volume
                            buy_record['fee'] = trade_price * trade_volume * 0.0001

                            # 更新总计数据
                            position_info['total_quantity'] = trade_volume
                            position_info['total_cost'] = trade_price * trade_volume
                            position_info['total_fee'] = trade_price * trade_volume * 0.0001
                    else:
                        # 旧格式：转换为新格式后更新
                        buy_record = {
                            'buy_price': trade_price,
                            'buy_time': trade_time,
                            'quantity': trade_volume,
                            'fee': trade_price * trade_volume * 0.0001,
                            'actual_amount': trade_price * trade_volume,
                            'order_id': f"TRADE_REPORT_{int(time.time())}",
                            'virtual': False,
                            'below_exp3_at_buy': False,
                            'crossed_exp3': False,
                            'real_trade': True
                        }

                        # 转换为新格式
                        new_position_info = {
                            'buy_queue': [buy_record],
                            'total_quantity': trade_volume,
                            'total_cost': trade_price * trade_volume,
                            'total_fee': trade_price * trade_volume * 0.0001
                        }

                        # 保留其他字段
                        for key, value in position_info.items():
                            if key not in ['buy_price', 'quantity', 'actual_amount', 'buy_time', 'fee']:
                                new_position_info[key] = value

                        # 更新持仓记录
                        self.position_records[code] = new_position_info
                        position_info = new_position_info
                    
                    # 记录价格调整
                    if abs(original_price - trade_price) > 0.001:
                        # 获取更新后的价格
                        if 'buy_queue' in position_info and position_info['buy_queue']:
                            new_price = position_info['buy_queue'][0]['buy_price']
                        else:
                            new_price = trade_price
                        self.add_record(f"已更新 {trade_code} 的买入价格: {original_price:.3f} -> {new_price:.3f}")
                        
                    # 保存更新后的持仓记录
                    self.position_records[trade_code] = position_info
                    
                # 卖出成交，需要确认卖出记录已添加到交易历史
                elif trade_direction == STOCK_SELL:
                    # 检查是否已经记录在交易历史中
                    trade_recorded = False
                    for record in self.trade_records:
                        if record.get('sell_order_id') == trade_order_id:
                            trade_recorded = True
                            break
                    
                    # 如果未记录且持仓中仍有该股票，执行卖出处理
                    if not trade_recorded and trade_code in self.position_records:
                        self.execute_sell(trade_code, trade_price, trade_time)
            
            # 更新持仓列表显示和交易汇总
            self.update_position_list()
            self.update_trade_summary()
            self.save_trading_data()
            
        except Exception as e:
            error_msg = f"处理成交回报出错: {str(e)}"
            self.add_record(error_msg)
            print(error_msg)  # 输出到控制台以便调试
    
    def process_all_trade_reports(self):
        """处理所有持仓的成交回报，确保价格更新"""
        try:
            # 只有在启用实际交易时才进行处理
            if not self.trading_enabled or not self.position_records:
                return

            # 处理所有持仓
            self.process_trade_reports()

        except Exception as e:
            self.add_record(f"处理所有成交回报时出错: {str(e)}")

    def query_trade_status(self, code, order_id):
        """查询指定委托的成交状态

        Args:
            code (str): 股票代码
            order_id (str): 委托号
        """
        try:
            if order_id not in self.order_tracking:
                return

            tracking_info = self.order_tracking[order_id]
            tracking_info['query_count'] += 1
            tracking_info['last_query_time'] = datetime.now()

            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')

            # 查询成交回报
            request = {
                'type': 'query_trades',
                'params': {
                    'start_date': today,
                    'end_date': today,
                    'stock_codes': [code],
                    'order_id': order_id
                }
            }

            response = self.send_request(request)

            if response['status'] != 'success':
                self.add_record(f"查询成交状态失败 {code} 委托号:{order_id}: {response.get('message', '未知错误')}")
                return

            # 检查是否有成交记录
            trades = response.get('data', [])
            if trades:
                # 有成交记录，处理成交回报
                for trade in trades:
                    trade_code = trade.get('stock_code', '')
                    trade_order_id = trade.get('order_id', '')
                    trade_volume = (trade.get('traded_volume') or
                                   trade.get('volume') or
                                   trade.get('quantity') or
                                   trade.get('deal_volume') or 0)
                    trade_price = (trade.get('traded_price') or
                                  trade.get('price') or
                                  trade.get('deal_price') or
                                  trade.get('avg_price') or 0)
                    # 尝试多种可能的方向字段名
                    trade_direction = (trade.get('direction') or
                                     trade.get('trade_direction') or
                                     trade.get('side') or
                                     trade.get('order_type') or
                                     trade.get('bs_flag') or 0)

                    # 添加调试信息，显示原始数据
                    self.add_record(f"[调试] 查询成交状态原始数据: {trade}")
                    self.add_record(f"[调试] 方向字段值: direction={trade.get('direction')}, trade_direction={trade.get('trade_direction')}, side={trade.get('side')}, order_type={trade.get('order_type')}, bs_flag={trade.get('bs_flag')}")

                    # 处理交易时间
                    trade_time_ms = trade.get('trade_time', 0)
                    if trade_time_ms:
                        trade_time = datetime.fromtimestamp(trade_time_ms/1000).strftime('%H:%M:%S')
                    else:
                        trade_time = datetime.now().strftime('%H:%M:%S')

                    if trade_code == code and trade_order_id == order_id and trade_volume > 0:
                        # 创建成交回报的唯一标识，避免重复显示
                        trade_cache_key = f"{trade_order_id}_{trade_time}_{trade_volume}_{trade_price:.3f}"

                        # 检查是否已经处理过这个成交回报
                        if trade_cache_key not in self.processed_trades_cache:
                            # 添加到已处理缓存
                            self.processed_trades_cache.add(trade_cache_key)

                            # 显示成交回报
                            # 修复方向显示问题：支持中文字符串和数字常量两种格式
                            if trade_direction == STOCK_BUY or trade_direction == '买入' or trade_direction == 'buy':
                                direction_text = '买入'
                            elif trade_direction == STOCK_SELL or trade_direction == '卖出' or trade_direction == 'sell':
                                direction_text = '卖出'
                            else:
                                # 对于未知的direction值，添加调试信息
                                direction_text = f'未知方向({trade_direction})'
                                self.add_record(f"[调试] 未知的交易方向值: {trade_direction}, STOCK_BUY={STOCK_BUY}, STOCK_SELL={STOCK_SELL}")

                            self.add_record(f"成交回报: {trade_code} 委托号:{trade_order_id} 方向:{direction_text} "
                                           f"成交量:{trade_volume} 成交价:{trade_price:.3f} 时间:{trade_time}")

                            # 标记委托已完成
                            tracking_info['is_completed'] = True

                            # 从委托跟踪中移除已完成的委托
                            if order_id in self.order_tracking:
                                del self.order_tracking[order_id]

                            # 从交易指令中移除已完成的委托
                            if code in self.trade_instructions and self.trade_instructions[code].get('order_id') == order_id:
                                del self.trade_instructions[code]

                            # 如果是卖出委托成交，清除对应的持仓记录
                            if trade_direction == STOCK_SELL:
                                self.clear_position_after_sell_confirmed(code, order_id)

                            return

            # 没有成交记录，显示未成交信息
            order_type_text = {
                'buy': '买入',
                'sell': '卖出',
                'protective_sell': '保护性卖出'
            }.get(tracking_info['order_type'], '未知')

            self.add_record(f"委托状态查询: {code} 委托号:{order_id} {order_type_text}委托未成交 (第{tracking_info['query_count']}次查询)")

        except Exception as e:
            self.add_record(f"查询成交状态出错 {code} 委托号:{order_id}: {str(e)}")

    def clear_position_after_sell_confirmed(self, code, order_id):
        """卖出成交确认后清除持仓记录"""
        try:
            if code not in self.position_records:
                return

            position_info = self.position_records[code]
            pending_sell = position_info.get('pending_sell')

            # 检查是否有待清除的卖出委托
            if not pending_sell or pending_sell.get('order_id') != order_id:
                return

            # 获取卖出信息
            sell_quantity = pending_sell['sell_quantity']
            buy_price = pending_sell['buy_price']

            # 更新持仓队列 - 移除已完全卖出的买入记录
            buy_queue = position_info.get('buy_queue', [])
            if buy_queue:
                buy_queue.pop(0)

            # 更新总计数据
            position_info['total_quantity'] = position_info.get('total_quantity', 0) - sell_quantity
            position_info['total_cost'] = position_info.get('total_cost', 0) - (buy_price * sell_quantity)

            # 清除待卖出标记
            if 'pending_sell' in position_info:
                del position_info['pending_sell']

            # 如果没有剩余持仓，删除整个记录
            if position_info.get('total_quantity', 0) <= 0 or len(buy_queue) == 0:
                del self.position_records[code]
                self.add_record(f"{code} 卖出成交确认，持仓已清空")
            else:
                self.add_record(f"{code} 卖出成交确认，剩余持仓: {position_info['total_quantity']}")

            # 移除等待盈利卖出标记
            if hasattr(self, 'waiting_for_profit_sell') and code in self.waiting_for_profit_sell:
                del self.waiting_for_profit_sell[code]

            # 更新界面和保存数据
            self.update_position_list()
            self.save_trading_data()

        except Exception as e:
            self.add_record(f"清除持仓记录失败 {code}: {str(e)}")

    def cleanup_timeout_pending_sells(self):
        """清理超时的待确认卖出记录（超过10分钟未确认的）"""
        try:
            current_time = datetime.now()
            timeout_codes = []

            for code, position_info in self.position_records.items():
                pending_sell = position_info.get('pending_sell')
                if not pending_sell:
                    continue

                # 检查时间戳
                timestamp_str = pending_sell.get('timestamp')
                if not timestamp_str:
                    continue

                try:
                    pending_time = datetime.fromisoformat(timestamp_str)
                    elapsed_minutes = (current_time - pending_time).total_seconds() / 60

                    # 如果超过10分钟未确认，强制清除
                    if elapsed_minutes > 10:
                        timeout_codes.append(code)
                        self.add_record(f"{code} 待确认卖出记录超时({elapsed_minutes:.1f}分钟)，强制清除")

                except ValueError:
                    # 时间戳格式错误，也清除
                    timeout_codes.append(code)
                    self.add_record(f"{code} 待确认卖出记录时间戳格式错误，强制清除")

            # 清除超时的记录
            for code in timeout_codes:
                if code in self.position_records:
                    position_info = self.position_records[code]
                    pending_sell = position_info.get('pending_sell')

                    if pending_sell:
                        # 执行延迟的持仓清除操作
                        sell_quantity = pending_sell['sell_quantity']
                        buy_price = pending_sell['buy_price']

                        # 更新持仓队列
                        buy_queue = position_info.get('buy_queue', [])
                        if buy_queue:
                            buy_queue.pop(0)

                        # 更新总计数据
                        position_info['total_quantity'] = position_info.get('total_quantity', 0) - sell_quantity
                        position_info['total_cost'] = position_info.get('total_cost', 0) - (buy_price * sell_quantity)

                        # 清除待卖出标记
                        del position_info['pending_sell']

                        # 如果没有剩余持仓，删除整个记录
                        if position_info.get('total_quantity', 0) <= 0 or len(buy_queue) == 0:
                            del self.position_records[code]

                        # 移除等待盈利卖出标记
                        if hasattr(self, 'waiting_for_profit_sell') and code in self.waiting_for_profit_sell:
                            del self.waiting_for_profit_sell[code]

            if timeout_codes:
                # 更新界面和保存数据
                self.update_position_list()
                self.save_trading_data()

        except Exception as e:
            self.add_record(f"清理超时待确认卖出记录失败: {str(e)}")

    def check_pending_orders(self):
        """检查待处理的委托，对超过5分钟未查询的委托进行查询"""
        try:
            current_time = datetime.now()
            orders_to_remove = []
            orders_to_query = []

            # 先收集需要处理的委托，避免在遍历时修改字典
            for order_id, tracking_info in list(self.order_tracking.items()):
                if tracking_info['is_completed']:
                    orders_to_remove.append(order_id)
                    continue

                # 计算距离上次查询的时间
                time_since_last_query = (current_time - tracking_info['last_query_time']).total_seconds()

                # 如果超过5分钟（300秒），进行查询
                if time_since_last_query >= 300:  # 5分钟 = 300秒
                    orders_to_query.append((tracking_info['code'], order_id))

            # 执行查询
            for code, order_id in orders_to_query:
                self.query_trade_status(code, order_id)

            # 清理已完成的委托跟踪
            for order_id in orders_to_remove:
                if order_id in self.order_tracking:
                    del self.order_tracking[order_id]

        except Exception as e:
            self.add_record(f"检查待处理委托时出错: {str(e)}")

    def retry_failed_order(self, failed_order):
        """重试失效的委托"""
        try:
            code = failed_order['stock_code']
            
            # 检查重试次数
            if not hasattr(self, 'retry_counts'):
                self.retry_counts = {}
            
            # 如果已经取消过交易，直接返回
            if code not in self.retry_counts:
                self.retry_counts[code] = 1
            else:
                # 如果已经达到3次，说明已经取消过交易，直接返回
                if self.retry_counts[code] >= 3:
                    self.add_record(f"{code} 委托连续失败3次，取消交易")
                    if code in self.trade_instructions:
                        del self.trade_instructions[code]  # 删除指令
                    if code in self.position_records:
                        del self.position_records[code]
                    return
                self.retry_counts[code] += 1
            
            # 重新获取最新价格
            current_price = self.get_current_price(code)
            if current_price:
                time_str = datetime.now().strftime('%H:%M:%S')

                # 检查是否在交易时间内
                if not self.is_trading_time():
                    self.add_record(f"[非交易时段] {code} 暂不执行重试委托")
                    return

                if code in self.trade_instructions:
                    instruction = self.trade_instructions[code]
                    if instruction['type'] == 'sell':
                        # 卖出重试，降价重试
                        new_price = current_price - 0.40
                        self.execute_sell(code, new_price, time_str)
                    else:
                        # 买入重试，加价重试
                        new_price = current_price + 0.40
                        self.execute_buy(code, new_price, time_str)
                        
        except Exception as e:
            self.add_record(f"重试委托失败 {code}: {str(e)}")

    def test_order_functions(self):
        """测试下单查询功能"""
        try:
            self.add_record("开始测试下单查询功能...")
            test_code = "127055.SZ"  # 使用大写的SZ
            test_records = []  # 记录测试过程中产生的委托号
            
            # 先测试连接
            test_request = {'type': 'query_asset'}
            test_response = self.send_request(test_request)
            if test_response['status'] != 'success':
                self.add_record("无法连接到委托服务器，请确保委托查询撤单程序已启动")
                return
            
            try:
                # 1. 测试买入委托
                price = 163.0
                time_str = datetime.now().strftime('%H:%M:%S')
                
                self.add_record(f"测试买入委托 {test_code}")
                request = {
                    'type': 'place_order',
                    'params': {
                        'stock_code': test_code,
                        'direction': STOCK_BUY,
                        'volume': 100,  # 测试用数量
                        'price_type': FIX_PRICE,
                        'price': price
                    }
                }
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    order_id = response['data']['order_id']
                    test_records.append(order_id)
                    self.add_record(f"测试买入委托已提交，委托号: {order_id}")
                
                # 等待3秒以确保买入委托请求完成
                self.add_record("等待3秒...")
                time.sleep(3)
                
                # 2. 查询委托状态
                request = {'type': 'query_orders'}
                response = self.send_request(request)
                
                if response['status'] == 'success':
                    self.add_record("委托查询成功:")
                    for order in response['data']:
                        status_map = {
                            48: "未报", 49: "待报", 50: "已报", 51: "已报待撤",
                            52: "部成待撤", 53: "部撤", 54: "已撤", 55: "部成",
                            56: "已成", 57: "废单", 255: "未知"
                        }
                        status = status_map.get(order['order_status'], "未知")
                        self.add_record(f"委托号: {order['order_id']}, 状态: {status}")
                
                # 3. 测试查询资产
                asset_request = {'type': 'query_asset'}
                asset_response = self.send_request(asset_request)
                
                if asset_response['status'] == 'success':
                    self.add_record("资产查询成功:")
                    self.add_record(f"可用资金: {asset_response['data']['cash']:,.2f}")
                
                # 4. 测试查询持仓
                position_request = {'type': 'query_positions'}
                position_response = self.send_request(position_request)

                if position_response['status'] == 'success':
                    self.add_record("持仓查询成功:")
                    self.add_record(f"持仓数量: {len(position_response['data'])}")
                    for i, pos in enumerate(position_response['data']):
                        self.add_record(f"持仓 {i+1}:")
                        self.add_record(f"  数据结构: {list(pos.keys())}")
                        for key, value in pos.items():
                            self.add_record(f"  {key}: {value} ({type(value).__name__})")
                        self.add_record("")  # 空行分隔
                
            finally:
                # 清理测试产生的委托
                self.add_record("开始清理测试委托...")
                
                # 撤销所有测试委托
                for order_id in test_records:
                    cancel_request = {
                        'type': 'cancel_order',
                        'params': {
                            'order_id': order_id
                        }
                    }
                    cancel_response = self.send_request(cancel_request)
                    if cancel_response['status'] == 'success':
                        self.add_record(f"已撤销测试委托 {order_id}")
                
                # 从持仓记录中移除测试股票
                if test_code in self.position_records:
                    del self.position_records[test_code]
                    self.update_position_list()
                    self.save_trading_data()
                
                self.add_record("测试清理完成")
            
            self.add_record("测试功能执行完成")
            
        except Exception as e:
            self.add_record(f"测试过程出错: {str(e)}")
            # 确保即使出错也尝试清理
            if test_code in self.position_records:
                del self.position_records[test_code]
                self.update_position_list()
                self.save_trading_data()

    def on_closing(self):
        """程序关闭时的清理工作"""
        try:
            # 停止监控
            self.monitoring = False

            # 等待监控线程结束
            if hasattr(self, 'monitor_thread') and self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2)  # 最多等待2秒

            # 保存数据
            self.save_trading_data()
            self.save_stock_profiles()

            # 销毁窗口
            self.root.destroy()
        except Exception as e:
            print(f"程序关闭时出错: {str(e)}")
            self.root.destroy()

    def run(self):
        """运行程序"""
        try:
            # 设置关闭事件处理
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        finally:
            # 程序关闭时保存交易数据和股票档案
            self.save_trading_data()
            self.save_stock_profiles()

    def cancel_order(self, code, order_id):
        """撤单操作"""
        try:
            request = {
                'type': 'cancel_order',
                'params': {
                    'order_id': order_id
                }
            }
            
            response = self.send_request(request)
            if response['status'] == 'success':
                self.add_record(f"撤单请求已提交 - {code} 委托号: {order_id}")
                self.add_record(f"撤单序号: {response['data'].get('cancel_seq')}")
                self.add_record(f"柜台合同编号: {response['data'].get('order_sysid')}")
                return True
            else:
                self.add_record(f"撤单失败 - {code} 委托号: {order_id}: {response.get('message', '未知错误')}")
                return False
            
        except Exception as e:
            self.add_record(f"撤单异常 - {code} 委托号: {order_id}: {str(e)}")
            return False

    def show_position_menu(self, event):
        """显示持仓右键菜单"""
        try:
            # 获取点击位置对应的项
            item = self.position_tree.identify_row(event.y)
            if item:
                # 选中该项
                self.position_tree.selection_set(item)
                # 显示菜单
                self.position_menu.post(event.x_root, event.y_root)
        except Exception as e:
            self.add_record(f"显示菜单失败: {str(e)}")

    def view_selected_position(self):
        """查看选中持仓的分时图"""
        selection = self.position_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        item = selection[0]
        values = self.position_tree.item(item)['values']
        code = values[0]  # 第一列是股票代码
        threading.Thread(target=lambda: self.check_and_show_chart(code)).start()

    def clear_selected_position(self):
        """清除选中的持仓记录"""
        selection = self.position_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        item = selection[0]
        values = self.position_tree.item(item)['values']
        code = values[0]  # 第一列是股票代码

        # 确认是否清除
        if messagebox.askyesno("确认", f"确定要清除 {code} 的持仓记录吗？\n注意：这只是清除记录，不会执行实际的卖出操作。"):
            if code in self.position_records:
                del self.position_records[code]
                self.add_record(f"已清除 {code} 的持仓记录")
                # 更新界面和保存数据
                self.update_position_list()
                self.update_trade_summary()
                self.save_trading_data()

    def clear_pending_orders(self):
        """清理所有未完成的委托"""
        try:
            # 获取当天日期
            today = datetime.now().strftime('%Y%m%d')
            
            # 获取所有委托，添加日期参数
            request = {
                'type': 'query_orders',
                'params': {
                    'start_date': today,
                    'end_date': today
                }
            }
            response = self.send_request(request)
            
            if response['status'] != 'success':
                self.add_record(f"查询委托失败: {response.get('message', '未知错误')}")
                return
            
            # 统计需要清理的委托
            pending_orders = [order for order in response['data']
                            if order['order_status'] in [48, 49, 50, 51, 52, 55]]  # 只处理未完成的委托
            
            if not pending_orders:
                self.add_record("没有需要清理的未完成委托")
                return
            
            self.add_record(f"开始清理 {len(pending_orders)} 个未完成委托...")
            
            # 撤销未完成的委托
            for order in pending_orders:
                cancel_request = {
                    'type': 'cancel_order',
                    'params': {
                        'order_id': order['order_id']
                    }
                }
                cancel_response = self.send_request(cancel_request)
                
                if cancel_response['status'] == 'success':
                    self.add_record(f"已撤销委托 - {order['stock_code']} 委托号:{order['order_id']}")
            
            # 清空重试计数器
            if hasattr(self, 'retry_counts'):
                self.retry_counts.clear()
            
            # 清空当日的持仓记录
            self.position_records.clear()
            self.update_position_list()
            self.save_trading_data()
            self.add_record("已清空当日持仓记录")
            
            self.add_record("清理完成")
            
        except Exception as e:
            self.add_record(f"清理委托时出错: {str(e)}")

    def check_connection(self):
        """检查与服务器的连接状态"""
        try:
            test_request = {'type': 'query_asset'}
            response = self.send_request(test_request)
            
            if response['status'] == 'success':
                if not self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 已连接", foreground="green")
                        self.root.update()
                    # 再添加记录
                    self.add_record("已连接到服务器")
                self.is_connected = True
            else:
                if self.is_connected:
                    # 先更新状态标签
                    if self.connection_status_label:
                        self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                        self.root.update()
                    # 再添加记录
                    self.add_record("与服务器断开连接")
                self.is_connected = False
                
        except Exception as e:
            if self.is_connected:
                # 先更新状态标签
                if self.connection_status_label:
                    self.connection_status_label.config(text="连接状态: 未连接", foreground="red")
                    self.root.update()
                # 再添加记录
                self.add_record(f"与服务器断开连接: {str(e)}")
            self.is_connected = False
        
        # 每30秒检查一次，降低资源占用
        self.root.after(30000, self.check_connection)

    def update_realtime_hint(self, text):
        """更新实时提示标签 - 已取消显示"""
        # 实时提示标签已被取消，此方法不再执行任何操作
        pass

    def update_check_status(self, text):
        """更新检查状态标签"""
        try:
            # 更新标签文本
            self.check_status_label.config(text=f"检查状态: {text}")

            # 根据状态类型设置颜色
            if "已检查" in text:
                self.check_status_label.config(foreground="green")
            elif "错过" in text or "失败" in text:
                self.check_status_label.config(foreground="red")
            elif "等待" in text:
                self.check_status_label.config(foreground="blue")
            else:
                self.check_status_label.config(foreground="black")
        except Exception as e:
            print(f"更新检查状态失败: {str(e)}")

    def calculate_indicators(self, df):
        try:
            # 创建DataFrame的副本以避免链式赋值警告
            df = df.copy()

            # 计算EMA50和EMA20
            df['ema50'] = df['close'].ewm(span=50, adjust=False).mean()
            df['ema20'] = df['close'].ewm(span=20, adjust=False).mean()

            # 计算CCI指标
            df['cci'] = self.calculate_cci(df, period=14)

            # 计算CCI上穿-100的信号
            df['cci_cross_up'] = (df['cci'] > -100) & (df['cci'].shift(1) <= -100)

            # 计算CCI连续大于100的周期数
            df['cci_above_100'] = df['cci'] > 100
            df['cci_consecutive_above_100'] = 0

            # 计算连续周期数
            consecutive_count = 0
            for i in range(len(df)):
                if df['cci_above_100'].iloc[i]:
                    consecutive_count += 1
                else:
                    consecutive_count = 0
                df.iloc[i, df.columns.get_loc('cci_consecutive_above_100')] = consecutive_count

            # 计算CCI跌破100的信号（前一天>100，当前<=100）
            df['cci_cross_down_100'] = (df['cci'] <= 100) & (df['cci'].shift(1) > 100)

            return df

        except Exception as e:
            print(f"计算指标时出错: {str(e)}")
            return None

    def calculate_cci(self, df, period=14):
        """计算CCI指标"""
        try:
            # 计算典型价格 TP = (High + Low + Close) / 3
            tp = (df['high'] + df['low'] + df['close']) / 3

            # 计算TP的简单移动平均
            sma_tp = tp.rolling(window=period).mean()

            # 计算平均绝对偏差
            mad = tp.rolling(window=period).apply(lambda x: np.abs(x - x.mean()).mean())

            # 计算CCI = (TP - SMA_TP) / (0.015 * MAD)
            cci = (tp - sma_tp) / (0.015 * mad)

            return cci

        except Exception as e:
            print(f"计算CCI指标时出错: {str(e)}")
            return pd.Series([np.nan] * len(df))

    def _check_trading_conditions(self, df, current_price, code, price_time=None):
        """检查交易条件并返回分析结果"""
        try:
            # 获取当前时间
            now = datetime.now()
            current_hour = now.hour
            current_minute = now.minute

            # 检查是否在交易时间
            is_trading = self.is_trading_time()

            # 获取当前和前一根K线的数据
            current_kline_data = self.get_current_kline_data(df, current_hour, current_minute, debug=False)
            prev_kline_data = self.get_prev_kline_data(df, current_hour, current_minute, debug=False)

            if current_kline_data.empty or prev_kline_data.empty:
                print(f"[调试] {code} K线数据不足，无法判断交叉信号")
                return False, None, None, None, None

            # 获取当前CCI和EMA50
            current_cci = current_kline_data['cci'].iloc[-1]
            prev_cci = prev_kline_data['cci'].iloc[-1]
            current_ema50 = current_kline_data['ema50'].iloc[-1]
            current_close = current_kline_data['close'].iloc[-1]

            # 检查CCI上穿-100信号
            cci_cross_up = current_kline_data['cci_cross_up'].iloc[-1]
            
            # 检查是否是第一次买入
            is_first_buy = True
            last_trade_type = None
            last_buy_price = None
            if code in self.stock_profiles:
                profile = self.stock_profiles[code]
                trades = profile.trades
                if trades:
                    last_trade = trades[-1]
                    last_trade_type = last_trade['type']
                    is_first_buy = last_trade_type == 'sell'
                    if not is_first_buy:
                        last_buy_price = profile.get_last_buy()

            # 新的买入条件判断
            # 条件1：CCI(14)从下往上穿越-100
            cci_condition = cci_cross_up

            # 条件2：close < EMA(50)
            ema_condition = current_close < current_ema50

            # 条件3：如果不是第一次买入，检查价格下跌幅度是否>=2%
            price_drop_condition = True
            if not is_first_buy and last_buy_price:
                price_drop = (last_buy_price - current_price) / last_buy_price * 100
                price_drop_condition = price_drop >= 2.0

            # 买入信号判断：CCI上穿-100 且 收盘价<EMA50 且 (首次买入或价格下跌>=2%)
            buy_signal = cci_condition and ema_condition and price_drop_condition
            
            # 卖出条件分析
            sell_info = None

            # 首先检查持仓记录，如果有持仓则进行分析
            if code in self.position_records:
                position_info = self.position_records[code]

                # 根据交易模式检查持仓类型
                should_analyze = False
                if self.trading_enabled:
                    # 实际交易模式：检查实际交易持仓
                    is_real_trade = position_info.get('real_trade', False)

                    # 如果没有real_trade标记，检查buy_queue中的virtual字段
                    if not is_real_trade and 'buy_queue' in position_info and position_info['buy_queue']:
                        first_buy = position_info['buy_queue'][0]
                        # 如果virtual为False，则认为是实际交易
                        if first_buy.get('virtual', False) == False:
                            is_real_trade = True
                            # 补充real_trade标记
                            position_info['real_trade'] = True
                            self.write_log(f"[修复] {code} 根据virtual=false补充real_trade标记")

                    should_analyze = is_real_trade
                    # 调试信息
                    if not should_analyze:
                        virtual_status = "unknown"
                        if 'buy_queue' in position_info and position_info['buy_queue']:
                            virtual_status = position_info['buy_queue'][0].get('virtual', 'missing')
                        self.write_log(f"[调试] {code} 持仓记录存在但real_trade={is_real_trade}, virtual={virtual_status}，跳过分析")
                else:
                    # 虚拟交易模式：检查虚拟持仓
                    is_virtual_position = False
                    if 'buy_queue' in position_info and position_info['buy_queue']:
                        # 新格式：检查队列中的第一个元素
                        is_virtual_position = position_info['buy_queue'][0].get('virtual', False)
                    else:
                        # 旧格式：检查顶层字段
                        is_virtual_position = position_info.get('virtual', False)
                    should_analyze = is_virtual_position
                    # 调试信息
                    if not should_analyze:
                        self.write_log(f"[调试] {code} 持仓记录存在但virtual={is_virtual_position}，跳过分析")

                # 如果匹配当前交易模式，进行持仓分析
                if should_analyze:
                    buy_queue = position_info.get('buy_queue', [])

                    if buy_queue:
                        # 新格式：使用队列结构
                        # 获取最早的买入记录进行盈利判断
                        earliest_buy = buy_queue[0]
                        buy_price = earliest_buy['buy_price']
                        quantity = earliest_buy['quantity']
                    elif 'buy_price' in position_info:
                        # 旧格式：直接获取买入价格
                        buy_price = position_info.get('buy_price')
                        quantity = position_info.get('quantity', 100)
                    else:
                        # 没有有效的持仓数据
                        sell_info = None
                        buy_price = None
                        quantity = None

                    if buy_price is not None and quantity is not None:
                        # 计算当前价格相对于买入价的涨幅
                        price_increase = (current_price - buy_price) / buy_price * 100
                        is_profitable = price_increase > 0

                        # 获取CCI相关数据
                        current_cci = current_kline_data['cci'].iloc[-1]
                        prev_cci = prev_kline_data['cci'].iloc[-1]
                        cci_consecutive_periods = current_kline_data['cci_consecutive_above_100'].iloc[-1]
                        prev_consecutive_periods = prev_kline_data['cci_consecutive_above_100'].iloc[-1]
                        cci_cross_down_100 = current_kline_data['cci_cross_down_100'].iloc[-1]

                        # 获取EMA20数据
                        current_ema20 = current_kline_data['ema20'].iloc[-1]

                        # 计算当前持仓仓位数
                        position_count = len(buy_queue) if buy_queue else 1

                        # 新的卖出条件：
                        # 1. 当只有一仓或卖出后只剩一仓时，涨幅达到5%后以EMA20对该单止盈
                        # 2. 其他情况保持原CCI条件
                        if position_count <= 1:
                            # 单仓情况：涨幅达到5%且价格跌破EMA20
                            sell_condition_met = (price_increase >= 5.0 and current_price < current_ema20)
                            sell_condition_type = "EMA20_STOP_PROFIT"
                        else:
                            # 多仓情况：保持原CCI条件
                            sell_condition_met = (is_profitable and
                                                prev_consecutive_periods >= 5 and
                                                cci_cross_down_100)
                            sell_condition_type = "CCI_PROFIT_SELL"

                        # 在非交易时段，如果满足卖出条件，保存状态以便交易时段使用
                        if not is_trading and sell_condition_met:
                            self.non_trading_indicator_cache[code] = {
                                'sell_condition_met': True,
                                'sell_condition_type': sell_condition_type,
                                'buy_price': buy_price,
                                'quantity': quantity,
                                'price_increase': price_increase,
                                'current_cci': current_cci,
                                'prev_cci': prev_cci,
                                'prev_consecutive_periods': prev_consecutive_periods,
                                'cci_cross_down_100': cci_cross_down_100,
                                'current_ema20': current_ema20,
                                'position_count': position_count,
                                'cache_time': now
                            }

                        # 在交易时段，检查是否有缓存的卖出条件
                        if is_trading and code in self.non_trading_indicator_cache:
                            cached_info = self.non_trading_indicator_cache[code]
                            # 如果缓存时间在合理范围内（比如1小时内），使用缓存的卖出条件
                            if (now - cached_info['cache_time']).total_seconds() < 3600:
                                sell_condition_met = cached_info['sell_condition_met']
                                sell_condition_type = cached_info.get('sell_condition_type', 'CCI_PROFIT_SELL')
                                # 清除缓存，避免重复使用
                                del self.non_trading_indicator_cache[code]

                        sell_info = {
                            'type': sell_condition_type if sell_condition_met else 'HOLD',
                            'min_buy_price': buy_price,
                            'min_buy_volume': quantity,
                            'price_increase': price_increase,
                            'is_profitable': is_profitable,
                            'current_cci': current_cci,
                            'prev_cci': prev_cci,
                            'cci_consecutive_periods': cci_consecutive_periods,
                            'prev_consecutive_periods': prev_consecutive_periods,
                            'cci_cross_down_100': cci_cross_down_100,
                            'current_ema20': current_ema20,
                            'position_count': position_count,
                            'sell_condition_type': sell_condition_type,
                            'sell_condition_met': sell_condition_met
                        }
                    else:
                        sell_info = None

            # 如果持仓记录中没有，再检查股票档案中的活跃持仓
            elif code in self.stock_profiles:
                profile = self.stock_profiles[code]
                # 调试信息
                self.write_log(f"[调试] {code} 检查股票档案: current_position={profile.current_position}, active_positions数量={len(profile.active_positions) if profile.active_positions else 0}")

                # 检查是否有当前持仓
                if profile.current_position > 0 and profile.active_positions:
                    # 获取最早的活跃持仓信息
                    earliest_position = min(profile.active_positions, key=lambda x: x['time'])
                    min_buy_price = earliest_position['price']
                    min_buy_volume = earliest_position['quantity']
                    # 计算当前价格相对于最低买入价的涨幅
                    price_increase = (current_price - min_buy_price) / min_buy_price * 100
                    is_profitable = price_increase > 0

                    # 获取CCI相关数据
                    current_cci = current_kline_data['cci'].iloc[-1]
                    prev_cci = prev_kline_data['cci'].iloc[-1]
                    cci_consecutive_periods = current_kline_data['cci_consecutive_above_100'].iloc[-1]
                    prev_consecutive_periods = prev_kline_data['cci_consecutive_above_100'].iloc[-1]
                    cci_cross_down_100 = current_kline_data['cci_cross_down_100'].iloc[-1]

                    # 获取EMA20数据
                    current_ema20 = current_kline_data['ema20'].iloc[-1]

                    # 计算当前持仓仓位数
                    position_count = len(profile.active_positions)

                    # 新的卖出条件：
                    # 1. 当只有一仓或卖出后只剩一仓时，涨幅达到5%后以EMA20对该单止盈
                    # 2. 其他情况保持原CCI条件
                    if position_count <= 1:
                        # 单仓情况：涨幅达到5%且价格跌破EMA20
                        sell_condition_met = (price_increase >= 5.0 and current_price < current_ema20)
                        sell_condition_type = "EMA20_STOP_PROFIT"
                    else:
                        # 多仓情况：保持原CCI条件
                        sell_condition_met = (is_profitable and
                                            prev_consecutive_periods >= 5 and
                                            cci_cross_down_100)
                        sell_condition_type = "CCI_PROFIT_SELL"

                    # 在非交易时段，如果满足卖出条件，保存状态以便交易时段使用
                    if not is_trading and sell_condition_met:
                        self.non_trading_indicator_cache[code] = {
                            'sell_condition_met': True,
                            'sell_condition_type': sell_condition_type,
                            'buy_price': min_buy_price,
                            'quantity': min_buy_volume,
                            'price_increase': price_increase,
                            'current_cci': current_cci,
                            'prev_cci': prev_cci,
                            'prev_consecutive_periods': prev_consecutive_periods,
                            'cci_cross_down_100': cci_cross_down_100,
                            'current_ema20': current_ema20,
                            'position_count': position_count,
                            'cache_time': now
                        }

                    # 在交易时段，检查是否有缓存的卖出条件
                    if is_trading and code in self.non_trading_indicator_cache:
                        cached_info = self.non_trading_indicator_cache[code]
                        # 如果缓存时间在合理范围内（比如1小时内），使用缓存的卖出条件
                        if (now - cached_info['cache_time']).total_seconds() < 3600:
                            sell_condition_met = cached_info['sell_condition_met']
                            sell_condition_type = cached_info.get('sell_condition_type', 'CCI_PROFIT_SELL')
                            # 清除缓存，避免重复使用
                            del self.non_trading_indicator_cache[code]

                    sell_info = {
                        'type': sell_condition_type if sell_condition_met else 'HOLD',
                        'min_buy_price': min_buy_price,
                        'min_buy_volume': min_buy_volume,
                        'price_increase': price_increase,
                        'is_profitable': is_profitable,
                        'current_cci': current_cci,
                        'prev_cci': prev_cci,
                        'cci_consecutive_periods': cci_consecutive_periods,
                        'prev_consecutive_periods': prev_consecutive_periods,
                        'cci_cross_down_100': cci_cross_down_100,
                        'current_ema20': current_ema20,
                        'position_count': position_count,
                        'sell_condition_type': sell_condition_type,
                        'sell_condition_met': sell_condition_met
                    }
            
            # 构建分析结果
            # 使用价格数据的时间，如果没有则使用系统时间
            if price_time is not None:
                time_str = price_time.strftime('%Y-%m-%d %H:%M:%S')
            else:
                time_str = now.strftime('%Y-%m-%d %H:%M:%S')

            analysis_result = {
                'time': time_str,
                'price': current_price,
                'cci': {
                    'current': current_cci,
                    'prev': prev_cci,
                    'cross_up': cci_cross_up,
                    'condition': cci_condition
                },
                'ema': {
                    'ema50': current_ema50,
                    'close': current_close,
                    'condition': ema_condition
                },
                'price_drop': {
                    'is_first_buy': is_first_buy,
                    'last_buy_price': last_buy_price,
                    'drop_pct': (last_buy_price - current_price) / last_buy_price * 100 if last_buy_price else 0,
                    'condition': price_drop_condition
                },
                'buy_signal': buy_signal,
                'sell_info': sell_info
            }
            
            # 返回信号和分析结果
            if buy_signal:
                return True, "golden", current_cci, "CCI", analysis_result
            elif sell_info and sell_info.get('sell_condition_met', False):
                # 根据卖出条件类型返回相应的信号
                signal_type = sell_info.get('sell_condition_type', 'CCI_PROFIT_SELL')
                return True, "death", sell_info['min_buy_volume'], signal_type, analysis_result

            return False, None, None, None, analysis_result

        except Exception as e:
            print(f"检查 {code} 交易条件失败: {str(e)}")
            return False, None, None, None, None

    # 保持原有的check_cross_signals函数作为别名
    check_cross_signals = _check_trading_conditions

    def print_cross_analysis(self, code, analysis_result):
        """打印并记录分析结果"""
        if not analysis_result:
            log_msg = f"[调试] {code} 无分析结果可打印"
            print(log_msg)
            self.write_log(log_msg)  # 使用write_log而不是add_record
            return
            
        # 构建分析日志
        log_lines = []
        log_lines.append(f"\n=== {code} 买卖信号分析 ===")
        log_lines.append(f"当前时间: {analysis_result['time']}")
        current_price = analysis_result.get('price', analysis_result.get('current_price', 0))
        log_lines.append(f"当前价格: {current_price:.3f}\n")

        # CCI条件分析
        log_lines.append("1. CCI(14)上穿-100条件:")
        log_lines.append(f"  - 前一K线CCI({analysis_result['cci']['prev']:.2f}) <= -100")
        log_lines.append(f"  - 当前K线CCI({analysis_result['cci']['current']:.2f}) > -100")
        log_lines.append(f"  - CCI上穿-100信号: {'是' if analysis_result['cci']['cross_up'] else '否'}")

        # EMA条件分析
        log_lines.append("\n2. 收盘价 < EMA(50)条件:")
        log_lines.append(f"  - 当前收盘价({analysis_result['ema']['close']:.3f}) < EMA50({analysis_result['ema']['ema50']:.3f}): {'是' if analysis_result['ema']['condition'] else '否'}")

        # 价格下跌条件分析
        log_lines.append("\n3. 价格下跌条件:")
        if analysis_result['price_drop']['is_first_buy']:
            log_lines.append("  - 第一次买入或上次为卖出，无需检查价格下跌")
        else:
            last_buy_price = analysis_result['price_drop']['last_buy_price']
            if last_buy_price:
                log_lines.append(f"  - 上次买入价格: {last_buy_price:.3f}")
                log_lines.append(f"  - 当前价格下跌幅度: {analysis_result['price_drop']['drop_pct']:.2f}%")
                log_lines.append(f"  - 下跌幅度要求(>=2%): {'满足' if analysis_result['price_drop']['condition'] else '不满足'}")

        # 买入条件汇总
        log_lines.append("\n买入条件汇总:")
        log_lines.append(f"  - CCI上穿-100: {'满足' if analysis_result['cci']['condition'] else '不满足'}")
        log_lines.append(f"  - 收盘价<EMA50: {'满足' if analysis_result['ema']['condition'] else '不满足'}")
        log_lines.append(f"  - 价格下跌条件: {'满足' if analysis_result['price_drop']['condition'] else '不满足'}")
        log_lines.append(f"  - 最终买入信号: {'满足' if analysis_result['buy_signal'] else '不满足'}\n")
        
        # 卖出条件分析
        log_lines.append("卖出条件分析:")
        # 检查是否有持仓（持仓记录或股票档案中的活跃持仓）
        if analysis_result['sell_info']:
            sell_info = analysis_result['sell_info']

            # 确定持仓来源
            position_source = ""
            if code in self.position_records:
                position_source = "持仓记录"
            elif code in self.stock_profiles:
                position_source = "股票档案"

            log_lines.append(f"持仓分析(来源:{position_source}):")
            log_lines.append(f"  - 买入价格: {sell_info['min_buy_price']:.3f}")
            log_lines.append(f"  - 持仓数量: {sell_info['min_buy_volume']}")
            log_lines.append(f"  - 当前价格涨幅: {sell_info['price_increase']:.2f}%")
            log_lines.append(f"  - 是否盈利: {'是' if sell_info['is_profitable'] else '否'}")
            log_lines.append(f"  - 持仓仓位数: {sell_info['position_count']}")
            log_lines.append(f"  - 卖出条件类型: {sell_info['sell_condition_type']}")

            if sell_info['sell_condition_type'] == 'EMA20_STOP_PROFIT':
                log_lines.append(f"EMA20止盈条件分析:")
                log_lines.append(f"  - 当前EMA20: {sell_info['current_ema20']:.3f}")
                log_lines.append(f"  - 当前价格: {current_price:.3f}")
                log_lines.append(f"  - 涨幅>=5%: {'是' if sell_info['price_increase'] >= 5.0 else '否'}")
                log_lines.append(f"  - 价格<EMA20: {'是' if current_price < sell_info['current_ema20'] else '否'}")
            else:
                log_lines.append(f"CCI指标分析:")
                log_lines.append(f"  - 当前CCI: {sell_info['current_cci']:.2f}")
                log_lines.append(f"  - 前一CCI: {sell_info['prev_cci']:.2f}")
                log_lines.append(f"  - 前一周期连续>100周期数: {sell_info['prev_consecutive_periods']}")
                log_lines.append(f"  - CCI跌破100: {'是' if sell_info['cci_cross_down_100'] else '否'}")

            if sell_info['sell_condition_met']:
                condition_desc = ""
                if sell_info['sell_condition_type'] == 'EMA20_STOP_PROFIT':
                    condition_desc = f"单仓EMA20止盈(涨幅{sell_info['price_increase']:.2f}%且价格跌破EMA20)"
                else:
                    condition_desc = "CCI盈利卖出(盈利且CCI连续5个周期以上>100后跌破100)"

                if self.is_trading_time():
                    log_lines.append(f"  - ✅ 满足卖出条件({condition_desc})，将执行卖出\n")
                else:
                    log_lines.append(f"  - ✅ 满足卖出条件({condition_desc})，但非交易时段，暂不执行卖出\n")
            else:
                reasons = []
                if sell_info['sell_condition_type'] == 'EMA20_STOP_PROFIT':
                    if sell_info['price_increase'] < 5.0:
                        reasons.append(f"涨幅不足5%(当前{sell_info['price_increase']:.2f}%)")
                    if current_price >= sell_info['current_ema20']:
                        reasons.append("价格未跌破EMA20")
                else:
                    if not sell_info['is_profitable']:
                        reasons.append("未盈利")
                    if sell_info['prev_consecutive_periods'] < 5:
                        reasons.append(f"CCI连续>100周期数不足5个(当前{sell_info['prev_consecutive_periods']}个)")
                    if not sell_info['cci_cross_down_100']:
                        reasons.append("CCI未跌破100")
                log_lines.append(f"  - ❌ 未满足卖出条件: {', '.join(reasons)}\n")
        else:
            # 添加调试信息
            has_position_record = code in self.position_records
            has_stock_profile = code in self.stock_profiles
            profile_info = ""
            if has_stock_profile:
                profile = self.stock_profiles[code]
                profile_info = f"档案持仓:{profile.current_position}, 活跃持仓:{len(profile.active_positions) if profile.active_positions else 0}"

            self.write_log(f"[调试] {code} 卖出条件分析: sell_info=None, 持仓记录={has_position_record}, 股票档案={has_stock_profile}, {profile_info}")
            log_lines.append("当前无持仓，不检查卖出条件\n")
        
        # 将分析结果只写入日志文件，不显示在界面上
        for line in log_lines:
            print(line)  # 保持控制台输出
            self.write_log(line)  # 只写入日志文件

        # 在界面上显示简要结果，并在满足卖出条件时实际执行卖出
        if analysis_result['buy_signal']:
            self.add_record(f"{code} 满足买入条件")
        elif analysis_result['sell_info'] and analysis_result['sell_info']['sell_condition_met']:
            # 显示卖出条件满足信息
            sell_info = analysis_result['sell_info']
            price_increase = sell_info['price_increase']
            condition_type = sell_info['sell_condition_type']

            if condition_type == 'EMA20_STOP_PROFIT':
                self.add_record(f"{code} 满足单仓EMA20止盈条件 (涨幅: {price_increase:.2f}%, 仓位: {sell_info['position_count']})")
            else:
                self.add_record(f"{code} 满足CCI卖出条件 (涨幅: {price_increase:.2f}%, 仓位: {sell_info['position_count']})")

            # 实际执行卖出操作
            if self.is_trading_time():
                # 获取当前价格
                current_price = self.get_current_price(code)
                if current_price:
                    self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
                else:
                    self.add_record(f"{code} 无法获取当前价格，暂不执行卖出")
            else:
                self.add_record(f"[非交易时段] {code} 暂不执行卖出")
    
    def get_current_kline_data(self, df, current_hour, current_minute, debug=True):
        """获取当前K线数据"""
        try:
            # 确保时间列是datetime类型
            df['time'] = pd.to_datetime(df['time'])

            # 定义30分钟K线的标准时间点（K线结束时间）
            trading_klines = ['10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00']

            # 计算当前应该使用的K线时间点
            target_time = None

            # 收盘后处理（15:00之后）
            if current_hour > 15:
                if debug: print(f"[调试] 当前K线：收盘后使用15:00的数据")
                target_time = '15:00'

            # 15:00收盘时处理
            elif current_hour == 15 and current_minute == 0:
                if debug: print(f"[调试] 当前K线：15:00收盘时使用15:00的数据")
                target_time = '15:00'

            # 开盘前处理（9:30之前）
            elif current_hour < 9 or (current_hour == 9 and current_minute < 30):
                if debug: print(f"[调试] 当前K线：开盘前使用前一交易日15:00的数据")
                target_time = '15:00'
                # 获取前一交易日的数据
                today = datetime.now().strftime('%Y%m%d')
                df = df[df['time'].dt.strftime('%Y%m%d') < today]

            # 早盘9:30-10:00特殊处理
            elif current_hour == 9 and current_minute >= 30:
                if debug: print(f"[调试] 当前K线：9:30-10:00时段使用前一交易日15:00的数据（因为当前K线尚未完成）")
                target_time = '15:00'
                # 获取前一交易日的数据
                today = datetime.now().strftime('%Y%m%d')
                df = df[df['time'].dt.strftime('%Y%m%d') < today]

            # 午休时间处理（11:30-13:00）
            elif (current_hour == 11 and current_minute > 30) or (current_hour == 12) or (current_hour == 13 and current_minute == 0):
                if debug: print(f"[调试] 当前K线：午休时间使用11:30的数据")
                target_time = '11:30'

            # 正常交易时段
            else:
                # 根据当前时间找到对应的K线时间点
                current_time_str = f"{current_hour:02d}:{current_minute:02d}"

                # 判断当前是否在某个K线时间段内
                if current_hour == 13 and current_minute < 30:
                    # 13:00-13:30时间段，当前K线尚未完成，使用上一个完整K线
                    target_time = '11:30'
                    if debug: print(f"[调试] 当前K线：13:00-13:30时间段，使用11:30的数据（当前K线未完成）")
                elif current_hour == 14 and current_minute < 30:
                    # 14:00-14:30时间段，使用13:30的数据
                    target_time = '13:30'
                    if debug: print(f"[调试] 当前K线：14:00-14:30时间段，使用13:30的数据（当前K线未完成）")
                else:
                    # 找到小于等于当前时间的最大K线时间点
                    valid_klines = [t for t in trading_klines if t <= current_time_str]
                    if valid_klines:
                        target_time = valid_klines[-1]
                        if debug: print(f"[调试] 当前K线：正常交易时段，使用{target_time}的数据")
                    else:
                        # 如果没有找到，使用前一交易日的最后一个K线
                        target_time = '15:00'
                        today = datetime.now().strftime('%Y%m%d')
                        df = df[df['time'].dt.strftime('%Y%m%d') < today]
                        if debug: print(f"[调试] 当前K线：无有效K线时间点，使用前一交易日15:00的数据")

            # 获取目标时间的K线数据
            current_data = df[df['time'].dt.strftime('%H:%M') == target_time]

            # 如果没有找到目标时间的数据，尝试使用最近的有效时间点
            if current_data.empty:
                available_times = sorted(df['time'].dt.strftime('%H:%M').unique())

                # 找到小于等于目标时间的最大时间点
                valid_times = [t for t in available_times if t <= target_time]
                if valid_times:
                    last_valid_time = valid_times[-1]
                    if debug: print(f"[调试] 当前K线：未找到{target_time}的数据，使用{last_valid_time}的数据")
                    current_data = df[df['time'].dt.strftime('%H:%M') == last_valid_time]
                else:
                    if debug: print(f"[调试] 当前K线：未找到任何有效的K线数据点")

            return current_data

        except Exception as e:
            print(f"获取当前K线数据失败: {str(e)}")
            return pd.DataFrame()

    def get_prev_kline_data(self, df, current_hour, current_minute, debug=True):
        """获取前一根K线数据"""
        try:
            # 确保时间列是datetime类型
            df['time'] = pd.to_datetime(df['time'])
            
            # 计算当前应该使用的K线时间点
            target_time = None
            use_prev_day = False
            
            # 收盘后处理（15:00之后）
            if current_hour > 15:
                if debug: print(f"[调试] 前一K线：收盘后使用14:30的数据")
                target_time = '14:30'

            # 15:00收盘时处理
            elif current_hour == 15 and current_minute == 0:
                if debug: print(f"[调试] 前一K线：15:00收盘时使用14:30的数据")
                target_time = '14:30'

            # 开盘前处理（9:30之前）
            elif current_hour < 9 or (current_hour == 9 and current_minute < 30):
                if debug: print(f"[调试] 前一K线：开盘前使用前一交易日14:30的数据")
                target_time = '14:30'
                use_prev_day = True

            # 早盘9:30-10:00特殊处理
            elif current_hour == 9 and current_minute >= 30:
                if debug: print(f"[调试] 前一K线：9:30-10:00时段使用前一交易日14:30的数据（因为没有9:30之前的K线）")
                target_time = '14:30'
                use_prev_day = True

            # 10:00特殊处理
            elif current_hour == 10 and current_minute == 0:
                if debug: print(f"[调试] 前一K线：10:00时段使用前一交易日15:00的数据（因为9:30的K线不存在）")
                target_time = '15:00'
                use_prev_day = True

            # 午休时间处理（11:30-13:00）
            elif (current_hour == 11 and current_minute > 30) or (current_hour == 12) or (current_hour == 13 and current_minute == 0):
                if debug: print(f"[调试] 前一K线：午休时间使用11:00的数据")
                target_time = '11:00'
            
            # 正常交易时段
            else:
                # 定义30分钟K线的标准时间点（K线结束时间）
                trading_klines = ['10:00', '10:30', '11:00', '11:30', '13:30', '14:00', '14:30', '15:00']

                # 根据当前时间找到对应的当前K线时间点
                current_time_str = f"{current_hour:02d}:{current_minute:02d}"

                # 判断当前是否在某个K线时间段内，确定当前K线
                current_kline_time = None
                if current_hour == 13 and current_minute < 30:
                    # 13:00-13:30时间段，当前K线尚未完成，当前使用的是11:30
                    current_kline_time = '11:30'
                elif current_hour == 14 and current_minute < 30:
                    # 14:00-14:30时间段，当前使用的是13:30
                    current_kline_time = '13:30'
                else:
                    # 找到小于等于当前时间的最大K线时间点
                    current_valid_klines = [t for t in trading_klines if t <= current_time_str]
                    if current_valid_klines:
                        current_kline_time = current_valid_klines[-1]

                if current_kline_time:
                    # 找到当前K线的前一个K线
                    current_kline_index = trading_klines.index(current_kline_time)
                    if current_kline_index > 0:
                        target_time = trading_klines[current_kline_index - 1]
                        if debug: print(f"[调试] 前一K线：正常交易时段，当前K线{current_kline_time}，使用{target_time}的数据")
                    else:
                        # 如果当前K线是第一个K线（10:00），使用前一交易日的最后一个K线
                        target_time = '15:00'
                        use_prev_day = True
                        if debug: print(f"[调试] 前一K线：当前是第一个K线，使用前一交易日15:00的数据")
                else:
                    # 如果没有找到当前K线，使用前一交易日的最后一个K线
                    target_time = '15:00'
                    use_prev_day = True
                    if debug: print(f"[调试] 前一K线：无有效当前K线，使用前一交易日15:00的数据")
            
            # 如果需要使用前一交易日的数据
            if use_prev_day:
                today = datetime.now().strftime('%Y%m%d')
                df = df[df['time'].dt.strftime('%Y%m%d') < today]
            
            # 获取目标时间的K线数据
            prev_data = df[df['time'].dt.strftime('%H:%M') == target_time]
            
            # 如果没有找到目标时间的数据，尝试使用最近的有效时间点
            if prev_data.empty:
                available_times = sorted(df['time'].dt.strftime('%H:%M').unique())

                # 找到小于目标时间的最大时间点
                valid_times = [t for t in available_times if t < target_time]
                if valid_times:
                    last_valid_time = valid_times[-1]
                    if debug: print(f"[调试] 前一K线：未找到{target_time}的数据，使用{last_valid_time}的数据；当前K线和前一K线是同一K线？")
                    prev_data = df[df['time'].dt.strftime('%H:%M') == last_valid_time]
                else:
                    if debug: print(f"[调试] 前一K线：未找到任何有效的K线数据点")
            
            return prev_data
            
        except Exception as e:
            print(f"获取前一根K线数据失败: {str(e)}")
            return pd.DataFrame()

    def start_price_refresh(self):
        """启动持仓价格定时刷新"""
        try:
            # 获取当前时间
            now = datetime.now()
            current_time = now.time()

            # 检查是否是15:01，如果是则进行特殊的持仓盈亏更新
            if current_time.hour == 15 and current_time.minute == 1:
                if not hasattr(self, '_updated_at_1501') or not self._updated_at_1501:
                    self.add_record("=== 15:01 持仓盈亏信息更新 ===")
                    self.update_position_summary_at_close()
                    self._updated_at_1501 = True
            elif current_time.hour != 15 or current_time.minute != 1:
                # 重置标记，以便下次15:01时能再次更新
                self._updated_at_1501 = False

            # 在交易时间内更频繁地同步持仓数据
            if self.is_trading_time() and self.trading_enabled and not self.is_virtual_trading():
                # 每5分钟同步一次服务器持仓数据
                if not hasattr(self, '_last_sync_time'):
                    self._last_sync_time = now
                elif (now - self._last_sync_time).total_seconds() >= 300:  # 5分钟
                    #self.add_record("定时同步服务器持仓数据...")
                    self.sync_server_positions()
                    self._last_sync_time = now

            # 更新持仓列表
            self.update_position_list()

            # 检查持仓盈利情况，盈利达到4000元即卖出
            if self.position_records:
                for code, info in list(self.position_records.items()):
                    # 在交易时间内使用实时价格
                    use_realtime = self.is_trading_time()
                    current_price = self.get_current_price(code, use_realtime=use_realtime)

                    # 检查是否是新的队列结构
                    if 'buy_queue' in info:
                        buy_queue = info.get('buy_queue', [])
                        if not buy_queue:
                            continue

                        # 使用最早的买入记录进行盈利判断
                        earliest_buy = buy_queue[0]
                        buy_price = earliest_buy['buy_price']
                        quantity = earliest_buy['quantity']
                    else:
                        # 兼容旧的结构
                        buy_price = info.get('buy_price')
                        quantity = info.get('quantity', 100)
                        if not buy_price:
                            continue

                    # 确保价格数据有效
                    if buy_price is not None and current_price is not None:
                        # 计算当前盈利
                        profit = (current_price - buy_price) * quantity

                        # 如果盈利达到4000元，执行卖出
                        if profit >= 4000:
                            self.add_record(f"{code} 盈利达到{profit:.2f}元，触发盈利卖出条件(≥4000元)")
                            # 只在交易时间执行卖出操作
                            if self.is_trading_time():
                                self.execute_sell(code, current_price, datetime.now().strftime('%H:%M:%S'))
                            else:
                                self.add_record(f"[非交易时段] {code} 暂不执行卖出")

            # 根据交易时间调整刷新频率
            if self.is_trading_time():
                # 交易时间内每15秒刷新一次，提高实时性
                refresh_interval = 15000
            else:
                # 非交易时间每30秒刷新一次
                refresh_interval = 30000

            self.root.after(refresh_interval, self.start_price_refresh)
        except Exception as e:
            self.add_record(f"刷新持仓价格时出错: {str(e)}")
            # 出错后仍然继续定时刷新
            self.root.after(30000, self.start_price_refresh)

    def sync_server_positions(self, show_completion_msg=False):
        """强制同步服务器持仓数据，解决持仓信息不一致问题

        Args:
            show_completion_msg: 是否显示同步完成信息，默认False
        """
        try:
            if not self.trading_enabled or self.is_virtual_trading():
                self.add_record("虚拟交易模式，跳过服务器持仓同步")
                return False

            #self.add_record("开始同步服务器持仓数据...")

            # 查询服务器持仓
            request = {'type': 'query_positions'}
            response = self.send_request(request)

            if response['status'] != 'success':
                self.add_record(f"查询服务器持仓失败: {response.get('message', '未知错误')}")
                return False



            # 处理服务器返回的持仓数据
            server_positions = {}
            for position in response['data']:
                # 尝试多种可能的字段名
                code = (position.get('stock_code') or
                       position.get('code') or
                       position.get('symbol') or '')

                volume = (position.get('volume') or
                         position.get('quantity') or
                         position.get('position') or 0)

                # 只处理可转债和ETF
                if not (code.startswith(('11', '12', '15', '5'))):
                    continue

                if volume <= 0:
                    continue

                # 尝试多种可能的字段名获取成本价
                cost_price = (position.get('cost_price') or
                             position.get('avg_price') or
                             position.get('average_price') or
                             position.get('buy_price') or
                             position.get('open_price'))

                # 尝试多种可能的字段名获取当前价
                current_price = (position.get('current_price') or
                               position.get('last_price') or
                               position.get('price') or
                               position.get('market_price'))

                # 尝试多种可能的字段名获取盈亏
                profit = (position.get('profit') or
                         position.get('pnl') or
                         position.get('unrealized_pnl') or
                         position.get('float_profit'))

                server_positions[code] = {
                    'volume': volume,
                    'cost_price': cost_price,
                    'current_price': current_price,
                    'profit': profit,
                    'market_value': position.get('market_value'),
                    'raw_data': position  # 保存原始数据用于调试
                }



            # 同步本地持仓记录
            sync_count = 0
            for code, server_pos in server_positions.items():
                local_pos = self.position_records.get(code, {})

                # 构建更新的持仓记录 - 使用新格式
                buy_record = {
                    'buy_price': server_pos['cost_price'] if server_pos['cost_price'] else local_pos.get('buy_price', 0),
                    'buy_time': local_pos.get('buy_time', datetime.now().strftime('%H:%M:%S')),
                    'quantity': server_pos['volume'],
                    'fee': local_pos.get('fee', (server_pos['cost_price'] if server_pos['cost_price'] else 0) * server_pos['volume'] * 0.0001),
                    'actual_amount': (server_pos['cost_price'] if server_pos['cost_price'] else 0) * server_pos['volume'],
                    'order_id': local_pos.get('order_id', f"SYNC_{int(time.time())}"),
                    'virtual': False,
                    'below_exp3_at_buy': local_pos.get('below_exp3_at_buy', False),
                    'crossed_exp3': local_pos.get('crossed_exp3', False),
                    'real_trade': True,
                    'last_sync_time': datetime.now().strftime('%H:%M:%S'),
                    'server_data': server_pos['raw_data']  # 保存服务器原始数据
                }

                updated_pos = {
                    'buy_queue': [buy_record],
                    'total_quantity': server_pos['volume'],
                    'total_cost': (server_pos['cost_price'] if server_pos['cost_price'] else 0) * server_pos['volume'],
                    'total_fee': local_pos.get('fee', (server_pos['cost_price'] if server_pos['cost_price'] else 0) * server_pos['volume'] * 0.0001)
                }

                self.position_records[code] = updated_pos
                sync_count += 1


            # 移除服务器上不存在的本地持仓
            removed_count = 0
            for code in list(self.position_records.keys()):
                position_info = self.position_records[code]

                # 检查是否有待清除的卖出委托，如果有则不删除，等待成交确认
                if 'pending_sell' in position_info:
                    self.add_record(f"{code} 有待确认的卖出委托，暂不清除持仓记录")
                    continue

                # 只删除实际交易的持仓记录，且服务器上不存在的
                if (code not in server_positions and
                    position_info.get('real_trade', False)):
                    del self.position_records[code]
                    removed_count += 1
                    self.add_record(f"{code} 服务器持仓不存在，已清除本地记录")

            # 更新界面显示
            self.update_position_list()
            self.save_trading_data()

            # 只在启动时或手动同步时显示详细的同步完成信息
            if show_completion_msg:
                self.add_record(f"持仓同步完成: 同步{sync_count}个持仓, 移除{removed_count}个持仓")
            return True

        except Exception as e:
            self.add_record(f"同步服务器持仓数据失败: {str(e)}")
            return False

    def manual_sync_positions(self):
        """手动触发持仓同步"""
        def sync_in_thread():
            try:
                self.add_record("手动触发持仓同步...")
                success = self.sync_server_positions(show_completion_msg=True)
                if success:
                    self.add_record("手动持仓同步完成")
                else:
                    self.add_record("手动持仓同步失败")
            except Exception as e:
                self.add_record(f"手动持仓同步异常: {str(e)}")

        # 在后台线程中执行同步，避免阻塞界面
        threading.Thread(target=sync_in_thread, daemon=True).start()









    def update_position_summary_at_close(self):
        """在15:01更新持仓盈亏汇总信息 - 使用与持仓列表相同的数据源和计算逻辑"""
        try:
            # 先同步服务器持仓数据
            if self.trading_enabled and not self.is_virtual_trading():
                self.sync_server_positions()

            if not self.position_records:
                self.add_record("当前无持仓，跳过盈亏汇总")
                return

            total_positions = 0
            total_cost = 0
            total_market_value = 0
            total_profit = 0
            profit_positions = 0
            loss_positions = 0
            max_profit = 0
            max_loss = 0
            max_profit_code = ""
            max_loss_code = ""

            # 获取服务器持仓数据（如果是实盘交易）
            server_positions = {}
            if self.trading_enabled and not self.is_virtual_trading():
                request = {'type': 'query_positions'}
                response = self.send_request(request)
                if response['status'] == 'success':
                    for position in response['data']:
                        code = position.get('stock_code', position.get('code', ''))
                        if code:
                            server_positions[code] = position

            self.add_record("持仓盈亏详情:")

            for code, info in self.position_records.items():
                # 根据交易模式过滤持仓
                should_include = False
                is_virtual = False
                if self.trading_enabled:
                    # 实际交易模式：只统计实际交易持仓
                    should_include = info.get('real_trade', False)
                else:
                    # 虚拟交易模式：只统计虚拟持仓
                    if 'buy_queue' in info and info['buy_queue']:
                        should_include = info['buy_queue'][0].get('virtual', False)
                        is_virtual = should_include
                    else:
                        should_include = info.get('virtual', False)
                        is_virtual = should_include

                if not should_include:
                    continue

                # 获取当前价格
                current_price = self.get_current_price(code)
                if not current_price:
                    continue

                # 获取持仓信息
                if 'buy_queue' in info and info['buy_queue']:
                    # 新格式
                    earliest_buy = info['buy_queue'][0]
                    buy_price = earliest_buy['buy_price']
                    quantity = info['total_quantity']
                else:
                    # 旧格式
                    buy_price = info.get('buy_price')
                    quantity = info.get('quantity', 0)

                if not buy_price or not quantity:
                    continue

                # 使用与display_position相同的计算逻辑
                profit = 0
                profit_percent = 0
                market_value = 0
                data_source = "本地"

                # 优先使用服务器数据（如果在实盘交易模式下）
                if not is_virtual and code in server_positions:
                    server_pos = server_positions[code]
                    server_market_value = server_pos.get('market_value')
                    server_cost_price = server_pos.get('cost_price')
                    server_profit = server_pos.get('profit')

                    if server_market_value and server_cost_price:
                        # 使用服务器市值和成本价计算盈亏
                        cost_value = server_cost_price * quantity
                        profit = server_market_value - cost_value
                        profit_percent = (profit / cost_value) * 100 if cost_value > 0 else 0
                        market_value = server_market_value
                        data_source = "服务器"
                    elif server_profit is not None:
                        # 直接使用服务器返回的盈亏
                        profit = server_profit
                        profit_percent = (profit / (buy_price * quantity)) * 100 if buy_price * quantity > 0 else 0
                        market_value = current_price * quantity
                        data_source = "服务器"
                    else:
                        # 服务器数据不完整，使用本地计算
                        profit_raw = (current_price - buy_price) * quantity
                        buy_fee = buy_price * quantity * 0.0003
                        sell_fee = current_price * quantity * 0.0003
                        total_fee = buy_fee + sell_fee
                        profit = profit_raw - total_fee
                        profit_percent = profit / (buy_price * quantity) * 100 if buy_price * quantity > 0 else 0
                        market_value = current_price * quantity
                        data_source = "本地(服务器数据不完整)"
                else:
                    # 虚拟交易或没有服务器数据，使用本地计算
                    profit_raw = (current_price - buy_price) * quantity
                    buy_fee = buy_price * quantity * 0.0003
                    sell_fee = current_price * quantity * 0.0003
                    total_fee = buy_fee + sell_fee
                    profit = profit_raw - total_fee
                    profit_percent = profit / (buy_price * quantity) * 100 if buy_price * quantity > 0 else 0
                    market_value = current_price * quantity
                    if is_virtual:
                        data_source = "虚拟"

                # 累计统计
                total_positions += 1
                total_cost += buy_price * quantity  # 使用买入价计算成本
                total_market_value += market_value
                total_profit += profit

                if profit > 0:
                    profit_positions += 1
                    if profit > max_profit:
                        max_profit = profit
                        max_profit_code = code
                else:
                    loss_positions += 1
                    if profit < max_loss:
                        max_loss = profit
                        max_loss_code = code

                # 显示个股盈亏，包含数据源标识
                status = "盈利" if profit > 0 else "亏损"
                self.add_record(f"  {code}: {status} {profit:+.2f}元 ({profit_percent:+.2f}%) [{data_source}] "
                              f"[买入:{buy_price:.3f} 现价:{current_price:.3f} 数量:{quantity}]")

            # 显示汇总信息
            self.add_record("=" * 50)
            trading_mode = "实际交易" if self.trading_enabled else "虚拟交易"
            data_source_note = "(使用与持仓列表相同的数据源)" if self.trading_enabled else ""
            self.add_record(f"持仓汇总 - {trading_mode} {data_source_note} (截至 {datetime.now().strftime('%H:%M:%S')}):")
            self.add_record(f"  总持仓数: {total_positions}")
            self.add_record(f"  盈利股票: {profit_positions} | 亏损股票: {loss_positions}")
            self.add_record(f"  总成本: {total_cost:,.2f}元")
            self.add_record(f"  总市值: {total_market_value:,.2f}元")
            profit_percent_text = f"({(total_profit/total_cost)*100:+.2f}%)" if total_cost > 0 else "(0.00%)"
            self.add_record(f"  总盈亏: {total_profit:+,.2f}元 {profit_percent_text}")

            if max_profit_code:
                self.add_record(f"  最大盈利: {max_profit_code} +{max_profit:.2f}元")
            if max_loss_code:
                self.add_record(f"  最大亏损: {max_loss_code} {max_loss:.2f}元")

            self.add_record("=" * 50)

            # 保存盈亏汇总到文件
            self.save_position_summary(total_positions, total_cost, total_market_value, total_profit,
                                     profit_positions, loss_positions, max_profit, max_loss,
                                     max_profit_code, max_loss_code)

        except Exception as e:
            self.add_record(f"更新持仓盈亏汇总失败: {str(e)}")

    def save_position_summary(self, total_positions, total_cost, total_market_value, total_profit,
                            profit_positions, loss_positions, max_profit, max_loss,
                            max_profit_code, max_loss_code):
        """保存持仓盈亏汇总到文件"""
        try:
            today = datetime.now().strftime('%Y%m%d')
            summary_file = f"持仓盈亏汇总_{today}.json"

            summary_data = {
                'date': today,
                'time': datetime.now().strftime('%H:%M:%S'),
                'trading_mode': '实际交易' if self.trading_enabled else '虚拟交易',
                'total_positions': total_positions,
                'profit_positions': profit_positions,
                'loss_positions': loss_positions,
                'total_cost': total_cost,
                'total_market_value': total_market_value,
                'total_profit': total_profit,
                'profit_percent': (total_profit/total_cost)*100 if total_cost > 0 else 0,
                'max_profit': max_profit,
                'max_profit_code': max_profit_code,
                'max_loss': max_loss,
                'max_loss_code': max_loss_code
            }

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)

            self.add_record(f"持仓盈亏汇总已保存到: {summary_file}")

        except Exception as e:
            self.add_record(f"保存持仓盈亏汇总失败: {str(e)}")

    def load_stock_profiles(self):
        """加载所有股票档案"""
        try:
            profile_dir = "stock_profiles"
            if not os.path.exists(profile_dir):
                os.makedirs(profile_dir)
                return

            for filename in os.listdir(profile_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(profile_dir, filename)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        profile = StockProfile.from_dict(data)
                        self.stock_profiles[profile.code] = profile
            
            self.add_record(f"已加载 {len(self.stock_profiles)} 只股票的交易档案")
            print(f"已加载 {len(self.stock_profiles)} 只股票的交易档案")
        except Exception as e:
            self.add_record(f"加载股票档案失败: {str(e)}")
            print(f"加载股票档案失败: {str(e)}")

    def save_stock_profiles(self):
        """保存所有股票档案"""
        try:
            profile_dir = "stock_profiles"
            if not os.path.exists(profile_dir):
                os.makedirs(profile_dir)

            for code, profile in self.stock_profiles.items():
                file_path = os.path.join(profile_dir, f"{code}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(profile.to_dict(), f, ensure_ascii=False, indent=2)
            
            self.add_record(f"已保存 {len(self.stock_profiles)} 只股票的交易档案")
        except Exception as e:
            self.add_record(f"保存股票档案失败: {str(e)}")

    def show_stock_profile(self, code):
        """显示股票档案"""
        if code not in self.stock_profiles:
            messagebox.showinfo("提示", f"未找到 {code} 的交易档案")
            return
        
        profile = self.stock_profiles[code]
        summary = profile.get_summary()
        
        # 创建新窗口显示档案信息
        profile_window = tk.Toplevel(self.root)
        profile_window.title(f"{code} 交易档案")
        profile_window.geometry("600x400")
        
        # 创建文本框显示汇总信息
        summary_text = tk.Text(profile_window, height=10, width=70)
        summary_text.pack(padx=10, pady=5)
        
        # 添加汇总信息
        summary_text.insert(tk.END, f"股票代码: {code}\n")
        summary_text.insert(tk.END, f"总交易次数: {summary['total_trades']}\n")
        summary_text.insert(tk.END, f"盈利交易: {summary['win_trades']}\n")
        summary_text.insert(tk.END, f"亏损交易: {summary['loss_trades']}\n")
        summary_text.insert(tk.END, f"总盈亏: {summary['total_profit']}元\n")
        summary_text.insert(tk.END, f"最大单笔盈利: {summary['max_profit']}元\n")
        summary_text.insert(tk.END, f"最大单笔亏损: {summary['max_loss']}元\n")
        summary_text.insert(tk.END, f"总手续费: {summary['total_fee']}元\n")
        summary_text.insert(tk.END, f"当前持仓: {summary['current_position']}股\n")
        if summary['last_buy_time']:
            summary_text.insert(tk.END, f"最近买入: {summary['last_buy_time']} 价格:{summary['last_buy_price']}\n")
        if summary['last_sell_time']:
            summary_text.insert(tk.END, f"最近卖出: {summary['last_sell_time']} 价格:{summary['last_sell_price']}\n")
        
        summary_text.config(state=tk.DISABLED)
        
        # 创建表格显示交易记录
        columns = ('时间', '类型', '价格', '数量', '手续费', '盈亏')
        tree = ttk.Treeview(profile_window, columns=columns, show='headings')
        
        # 设置列标题
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=95)
        
        # 添加交易记录
        for trade in profile.trades:
            values = (
                trade['time'],
                '买入' if trade['type'] == 'buy' else '卖出',
                f"{trade['price']:.3f}",
                trade['quantity'],
                f"{trade['fee']:.2f}",
                f"{trade.get('profit', '-')}"
            )
            tree.insert('', tk.END, values=values)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(profile_window, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        tree.pack(expand=True, fill=tk.BOTH, padx=10, pady=5)

    def view_stock_profile(self):
        """查看选中持仓的交易档案"""
        selection = self.position_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "请先选择一个持仓")
            return

        item = selection[0]
        values = self.position_tree.item(item)['values']
        code = values[0]  # 第一列是股票代码
        self.show_stock_profile(code)



def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.Series:
    # 计算MTR
    high_low = df['high'] - df['low']
    high_close = abs(df['close'].shift() - df['high'])
    low_close = abs(df['close'].shift() - df['low'])
    mtr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    
    # 计算ATR - 使用简单移动平均
    atr = mtr.rolling(window=period).mean()
    
    return atr

if __name__ == "__main__":
    try:
        app = TimeSeriesViewer()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}") 